/* eslint-disable node/no-unpublished-import */
import { fixupConfigRules, fixupPluginRules } from "@eslint/compat";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import _import from "eslint-plugin-import";
import node from "eslint-plugin-node";
import promise from "eslint-plugin-promise";
import tsParser from "@typescript-eslint/parser";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default [{
    ignores: [
        "**/jest.config.js",
        "**/coverage",
        "**/build",
        "**/dist",
        "**/*.css",
        "**/*.svg",
        "**/*.spec.ts",
        "**/*.test.ts",
        "**/jest.config.js",
        "**/drizzle.config.ts",
    ],
}, ...fixupConfigRules(compat.extends(
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:import/typescript",
    "plugin:node/recommended",
    "plugin:promise/recommended",
)), {
    plugins: {
        "@typescript-eslint": fixupPluginRules(typescriptEslint),
        import: fixupPluginRules(_import),
        node: fixupPluginRules(node),
        promise: fixupPluginRules(promise),
    },

    languageOptions: {
        parser: tsParser,
        ecmaVersion: 2021,
        sourceType: "module",

        parserOptions: {
            project: "./tsconfig.json",
        },
    },

    rules: {
        "no-console": "off",
        "no-process-exit": "off",
        "import/no-unresolved": "off",
        "node/no-missing-import": "off",
        "@typescript-eslint/no-redeclare": "off",
        "@typescript-eslint/no-this-alias": "off",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/naming-convention": "off",
        "@typescript-eslint/no-empty-interface": "off",
        "node/no-unsupported-features/es-syntax": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "node/no-unsupported-features/node-builtins": "off",
        "node/no-unsupported-features/es-builtins": "off",
        "comma-dangle": "off",
    },
}];