/**
 * @fileoverview Main application setup and configuration
 * Sets up Express server with middleware, security features, and route handling
 */
import './utils/env';
import cors from 'cors';
import express from 'express';
import { mw as requestIp } from 'request-ip';
import { errorHandler, handle404Error } from '@/utils/errors';
import routes from '@/routes/routes';

import { logger } from './utils/logger';
import rateLimiter from './utils/rateLimit';

// Constants

const app = express();

app.use(express.json());
app.use(cors());
app.use(requestIp());
// Rate limiting middleware to prevent DDoS attacks
app.use(rateLimiter);

app.use(logger);
app.use('/', routes);
app.all('*', handle404Error);

app.use(errorHandler);

export default app;
