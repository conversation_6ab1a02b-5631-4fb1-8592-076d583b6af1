/**
 * @fileoverview Server initialization and startup
 * Handles server configuration, environment validation, and graceful shutdown
 */

import app from './app';
import process from 'node:process';
import { consola } from 'consola';
import { z } from 'zod';

// Validate required environment variables
const envSchema = z.object({
  PORT: z.string().transform(Number).pipe(z.number().positive()),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development')
});

try {
  const env = envSchema.parse(process.env);
  const server = app.listen(env.PORT, () => {
    consola.success(
      `Server running in ${env.NODE_ENV} mode at http://localhost:${env.PORT}`
    );
  });

  // Handle graceful shutdown
  const gracefulShutdown = () => {
    consola.info('Received shutdown signal. Closing server...');
    server.close(() => {
      consola.success('Server closed successfully');
      process.exit(0);
    });

    // Force close after 10s
    setTimeout(() => {
      consola.error(
        'Could not close connections in time, forcefully shutting down'
      );
      process.exit(1);
    }, 10000);
  };

  process.on('SIGTERM', gracefulShutdown);
  process.on('SIGINT', gracefulShutdown);
} catch (error) {
  if (error instanceof z.ZodError) {
    consola.error('Environment validation failed:', error.errors);
  } else {
    consola.error('Failed to start server:', error);
  }
  process.exit(1);
}
