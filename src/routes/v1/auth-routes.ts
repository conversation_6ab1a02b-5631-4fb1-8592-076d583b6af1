import { handleHealthCheck } from '@/controllers/common/health-controller';
import {
  handleCreateAccount,
  handleLogin,
  handleVerifyOtp
} from '@/controllers/v1/auth-controller';
import { createRouter } from '@/utils/router';
import type { Router } from 'express';

const authRouter = createRouter((router: Router) => {
  router.get('/login', handleLogin);
  router.get('/create-account', handleCreateAccount);
  router.get('/verify-otp', handleVerifyOtp);
});

export default authRouter;
