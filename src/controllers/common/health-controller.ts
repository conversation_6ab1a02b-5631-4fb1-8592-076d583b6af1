/**
 * @fileoverview Health check endpoint controller
 * Provides system health information including uptime, memory usage, and environment details
 */

import { create<PERSON>and<PERSON> } from '@/utils/handler';
import { z } from 'zod';
import process from 'node:process';
import os from 'node:os';

// Schema for health check response
const healthSchema = {
  request: {},
  response: {
    body: z.object({
      status: z.enum(['healthy', 'degraded', 'unhealthy']),
      message: z.string(),
      timestamp: z.number(),
      system: z.object({
        uptime: z.number(),
        nodeVersion: z.string(),
        memory: z.object({
          total: z.number(),
          free: z.number(),
          used: z.number(),
          heapUsed: z.number()
        }),
        cpu: z.object({
          cores: z.number(),
          loadAvg: z.array(z.number())
        })
      }),
      env: z.object({
        nodeEnv: z.string()
      })
    })
  }
} as const;

/**
 * Handles health check requests
 * Returns detailed system health information including memory usage, CPU load, and environment details
 */
export const handleHealthCheck = createHandler(
  healthSchema,
  async (_req, res) => {
    const memoryUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();

    // Calculate status based on system metrics
    const heapUsedPercentage =
      (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    const status = heapUsedPercentage > 90 ? 'degraded' : 'healthy';

    res.json({
      status,
      message:
        status === 'healthy'
          ? 'System is healthy'
          : 'System is under heavy load',
      timestamp: Date.now(),
      system: {
        uptime: process.uptime(),
        nodeVersion: process.version,
        memory: {
          total: totalMemory,
          free: freeMemory,
          used: totalMemory - freeMemory,
          heapUsed: memoryUsage.heapUsed
        },
        cpu: {
          cores: os.cpus().length,
          loadAvg: os.loadavg()
        }
      },
      env: {
        nodeEnv: process.env.NODE_ENV || 'development'
      }
    });
  }
);
