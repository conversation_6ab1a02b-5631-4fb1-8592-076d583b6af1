import { createHand<PERSON> } from '@/utils/handler';
import { z } from 'zod';

const welcomeSchema = {
  response: {
    body: z.object({
      message: z.string()
    })
  }
} as const;

export const handleWelcome = createHandler(welcomeSchema, async (req, res) => {
  // TypeScript ensures email exists because of our schema
  const { email } = req.query;

  res.json({
    message: `Welcome to the API!`
  });
});
