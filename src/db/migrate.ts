import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import dotenv from 'dotenv';
dotenv.config();

const main = async () => {
  const migrationClient = postgres(process.env.DB_URL, { max: 1 });
  try {
    const db = drizzle(migrationClient);
    await migrate(db, {
      migrationsFolder: './src/db/migrations'
    });
    console.log('Migration successful');
    process.exit(0);
  } catch (error) {
    console.log(error);
    process.exit(1);
  } finally {
    await migrationClient.end();
  }
};

main();
