import {
  pgTable,
  foreignKey,
  serial,
  integer,
  varchar,
  text,
  timestamp
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// Encounters Values table schema
export const encountersValues = pgTable(
  'encounters_values',
  {
    valueId: serial('value_id').primaryKey().notNull(),
    summaryId: integer('summary_id').notNull(),
    key: varchar({ length: 255 }),
    value: text(),
    data: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
  },
  (table) => [
    foreignKey({
      columns: [table.summaryId],
      foreignColumns: [table.valueId], // Will be updated to reference userHealthSummary.summaryId
      name: 'encounters_values_summary_id_fkey'
    })
  ]
);

// Encounters Values relations
export const encountersValuesRelations = relations(encountersValues, ({ one }) => ({
  userHealthSummary: one(userHealthSummary, {
    fields: [encountersValues.summaryId],
    references: [userHealthSummary.summaryId]
  })
}));
