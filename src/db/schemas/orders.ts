import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  numeric,
  pgEnum
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';
import { sql } from 'drizzle-orm';

// Enums related to orders table
export const enumOrdersStatus = pgEnum('enum_orders_status', [
  'pending',
  'started',
  'completed',
  'cancelled',
  'failed',
  'no_show',
  'rescheduled'
]);

export const enumOrdersType = pgEnum('enum_orders_type', [
  'video',
  'audio',
  'chat',
  'in_person'
]);

export const enumOrdersCategory = pgEnum('enum_orders_category', [
  'consultation',
  'follow_up',
  'emergency',
  'routine'
]);

// Orders table schema
export const orders = pgTable(
  'orders',
  {
    id: serial().primaryKey().notNull(),
    orderId: text('order_id').notNull(),
    scheduleId: integer('schedule_id'),
    startTime: timestamp('start_time', { withTimezone: true, mode: 'string' }),
    endTime: timestamp('end_time', { withTimezone: true, mode: 'string' }),
    callerId: integer('caller_id'),
    calleeId: integer('callee_id'),
    doctorId: integer('doctor_id'),
    status: enumOrdersStatus(),
    type: enumOrdersType(),
    category: enumOrdersCategory(),
    conversationMode: text('conversation_mode'),
    cost: numeric(),
    billed: boolean().default(false),
    callerLocation: varchar('caller_location', { length: 255 }),
    calleeLocation: varchar('callee_location', { length: 255 }),
    instructions: text(),
    diagnosis: text(),
    procedure: text(),
    orderGuid: text('order_guid').default(sql`uuid_generate_v4()`).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    sessionId: varchar('session_id', { length: 255 }),
    archiveId: varchar('archive_id', { length: 255 }),
    recordingUrl: text('recording_url'),
    recordingStatus: varchar('recording_status', { length: 255 }),
    transcriptionStatus: varchar('transcription_status', { length: 255 }),
    transcriptionUrl: text('transcription_url'),
    paymentStatus: varchar('payment_status', { length: 255 }),
    refundStatus: varchar('refund_status', { length: 255 }),
    refundAmount: numeric('refund_amount'),
    refundReason: text('refund_reason'),
    cancelReason: text('cancel_reason'),
    noShowReason: text('no_show_reason'),
    rescheduleReason: text('reschedule_reason'),
    rating: integer(),
    feedback: text(),
    followUpRequired: boolean('follow_up_required').default(false),
    followUpDate: timestamp('follow_up_date', { withTimezone: true, mode: 'string' }),
    prescriptionRequired: boolean('prescription_required').default(false),
    labOrderRequired: boolean('lab_order_required').default(false),
    referralRequired: boolean('referral_required').default(false),
    notes: text(),
    metadata: text(),
    externalOrderId: varchar('external_order_id', { length: 255 }),
    source: varchar({ length: 255 }),
    priority: varchar({ length: 255 }),
    tags: text(),
    customFields: text('custom_fields')
  },
  (table) => [
    foreignKey({
      columns: [table.callerId],
      foreignColumns: [table.id], // This will be updated to reference users.userId
      name: 'orders_caller_id_fkey'
    }),
    foreignKey({
      columns: [table.calleeId],
      foreignColumns: [table.id], // This will be updated to reference users.userId
      name: 'orders_callee_id_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [table.id], // This will be updated to reference users.userId
      name: 'orders_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.scheduleId],
      foreignColumns: [table.id], // This will be updated to reference schedules.scheduleId
      name: 'orders_schedule_id_fkey'
    })
  ]
);
