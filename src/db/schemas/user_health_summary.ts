import {
  pgTable,
  foreignKey,
  serial,
  integer,
  varchar,
  text,
  timestamp,
  boolean
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';
import { users } from './users';
import { encountersValues } from './encounters_values';
import {
  allergiesValues,
  carePlanValues,
  diagnosesValues,
  documentValues,
  familyHistoryValues,
  immunizationsValues,
  medicationsValues,
  proceduresValues,
  resultsValues,
  socialHistoryValues
} from '../migrations/schema';

// User Health Summary table schema
export const userHealthSummary = pgTable(
  'user_health_summary',
  {
    summaryId: serial('summary_id').primaryKey().notNull(),
    userId: integer('user_id').notNull(),
    summaryType: varchar('summary_type', { length: 255 }),
    summaryData: text('summary_data'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    summaryDate: timestamp('summary_date', {
      withTimezone: true,
      mode: 'string'
    }),
    summarySource: varchar('summary_source', { length: 255 }),
    summaryVersion: varchar('summary_version', { length: 50 }),
    summaryStatus: varchar('summary_status', { length: 100 }),
    summaryNotes: text('summary_notes'),
    summaryMetadata: text('summary_metadata'),
    externalId: varchar('external_id', { length: 255 }),
    syncStatus: varchar('sync_status', { length: 50 }),
    lastSyncAt: timestamp('last_sync_at', {
      withTimezone: true,
      mode: 'string'
    })
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [table.summaryId], // Will be updated to reference users.userId
      name: 'user_health_summary_user_id_fkey'
    })
  ]
);

// User Health Summary relations
export const userHealthSummaryRelations = relations(
  userHealthSummary,
  ({ one, many }) => ({
    user: one(users, {
      fields: [userHealthSummary.userId],
      references: [users.userId]
    }),
    encountersValues: many(encountersValues),
    allergiesValues: many(allergiesValues),
    careplanValues: many(carePlanValues),
    diagnosesValues: many(diagnosesValues),
    documentValues: many(documentValues),
    familyHistoryValues: many(familyHistoryValues),
    immunizationsValues: many(immunizationsValues),
    medicationsValues: many(medicationsValues),
    proceduresValues: many(proceduresValues),
    resultsValues: many(resultsValues),
    socialHistoryValues: many(socialHistoryValues)
  })
);
