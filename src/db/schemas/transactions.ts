import {
  pgTable,
  foreignKey,
  text,
  integer,
  doublePrecision,
  timestamp,
  varchar,
  pgEnum
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';
import { sql } from 'drizzle-orm';
import { users } from './users';
import { paymentDetails } from '../migrations/schema';

// Enums related to transactions table
export const enumTransactionsPaymentMethodType = pgEnum(
  'enum_transactions_payment_method_type',
  ['STRIPE', 'BRAINTREE', 'PAYPAL', 'RECURLY', 'AUTHORISED_NET', 'NMI']
);

export const enumTransactionsPaymentStatus = pgEnum(
  'enum_transactions_payment_status',
  ['pending', 'completed', 'errored', 'cancelled', 'RECURLY', 'AUTHORISED_NET']
);

export const enumTransactionsRefundPaymentStatus = pgEnum(
  'enum_transactions_refund_payment_status',
  ['succeeded', 'failed', 'pending', 'n/a']
);

export const enumTransactionsStatus = pgEnum('enum_transactions_status', [
  'succeeded',
  'failed',
  'pending'
]);

export const enumTransactionsTransactionStatus = pgEnum(
  'enum_transactions_transaction_status',
  ['initiated', 'completed']
);

// Transactions table schema
export const transactions = pgTable(
  'transactions',
  {
    transactionId: text('transaction_id').primaryKey().notNull(),
    payerUserId: integer('payer_user_id').notNull(),
    payeeUserId: integer('payee_user_id'),
    amount: doublePrecision().notNull(),
    currency: text().notNull(),
    status: enumTransactionsStatus().notNull(),
    description: text(),
    errorDescription: text('error_description'),
    cardDetails: text('card_details'),
    lineItems: text('line_items'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    globalId: varchar('global_id', { length: 100 }),
    refundTransactionId: varchar('refund_transaction_id', { length: 100 }),
    orderGuid: text('order_guid')
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    paymentDetailsId: integer('payment_details_id'),
    paymentMethodType: enumTransactionsPaymentMethodType(),
    paymentStatus: enumTransactionsPaymentStatus(),
    refundPaymentStatus: enumTransactionsRefundPaymentStatus(),
    transactionStatus: enumTransactionsTransactionStatus()
  },
  (table) => [
    foreignKey({
      columns: [table.payeeUserId],
      foreignColumns: [table.payerUserId], // Will be updated to reference users.userId
      name: 'transactions_payee_user_id_fkey'
    }),
    foreignKey({
      columns: [table.payerUserId],
      foreignColumns: [table.payeeUserId], // Will be updated to reference users.userId
      name: 'transactions_payer_user_id_fkey'
    }),
    foreignKey({
      columns: [table.paymentDetailsId],
      foreignColumns: [table.payerUserId], // Will be updated to reference paymentDetails.paymentDetailsId
      name: 'transactions_payment_details_id_fkey'
    })
  ]
);

// Transactions relations
export const transactionsRelations = relations(transactions, ({ one }) => ({
  payeeUser: one(users, {
    fields: [transactions.payeeUserId],
    references: [users.userId],
    relationName: 'transactions_payeeUserId_users_userId'
  }),
  payerUser: one(users, {
    fields: [transactions.payerUserId],
    references: [users.userId],
    relationName: 'transactions_payerUserId_users_userId'
  }),
  paymentDetail: one(paymentDetails, {
    fields: [transactions.paymentDetailsId],
    references: [paymentDetails.paymentDetailsId]
  })
}));
