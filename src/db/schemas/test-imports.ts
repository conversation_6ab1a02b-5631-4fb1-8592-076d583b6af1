// Test file to verify that the refactored schema structure works correctly
// This file can be deleted after verification

// Test importing from individual files
import { users, enumUsersRole, enumUsersStatus, enumUsersGender } from './users';
import { orders, enumOrdersStatus, enumOrdersType, enumOrdersCategory } from './orders';
import { consultNotes, enumConsultNotesStatus } from './consult_notes';
import { schedules, enumSchedulesStatus, enumSchedulesType } from './schedules';
import { pharmacies } from './pharmacies';
import { states } from './states';
import { tennantMaster } from './tennant_master';
import { telehealthServiceOrder, enumTelehealthServiceOrderStatus, enumTelehealthServiceOrderType } from './telehealth_service_order';
import { drugs } from './drugs';
import { drugsCategory } from './drugs_category';

// Test importing from the index file (backward compatibility)
import {
  users as usersFromIndex,
  orders as ordersFromIndex,
  consultNotes as consultNotesFromIndex,
  schedules as schedulesFromIndex,
  pharmacies as pharmaciesFromIndex,
  states as statesFromIndex,
  tennantMaster as tennantMasterFromIndex,
  telehealthServiceOrder as telehealthServiceOrderFromIndex,
  drugs as drugsFromIndex,
  drugsCategory as drugsCategoryFromIndex
} from './index';

// Test that the imports are working correctly
console.log('✅ Individual imports working');
console.log('✅ Index imports working');

// Test that table schemas are properly defined
const testTables = {
  users,
  orders,
  consultNotes,
  schedules,
  pharmacies,
  states,
  tennantMaster,
  telehealthServiceOrder,
  drugs,
  drugsCategory
};

const testTablesFromIndex = {
  usersFromIndex,
  ordersFromIndex,
  consultNotesFromIndex,
  schedulesFromIndex,
  pharmaciesFromIndex,
  statesFromIndex,
  tennantMasterFromIndex,
  telehealthServiceOrderFromIndex,
  drugsFromIndex,
  drugsCategoryFromIndex
};

// Test that enums are properly exported
const testEnums = {
  enumUsersRole,
  enumUsersStatus,
  enumUsersGender,
  enumOrdersStatus,
  enumOrdersType,
  enumOrdersCategory,
  enumConsultNotesStatus,
  enumSchedulesStatus,
  enumSchedulesType,
  enumTelehealthServiceOrderStatus,
  enumTelehealthServiceOrderType
};

console.log('✅ All table schemas defined:', Object.keys(testTables));
console.log('✅ All enums defined:', Object.keys(testEnums));
console.log('✅ Backward compatibility verified');

export {
  testTables,
  testTablesFromIndex,
  testEnums
};
