import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// Drugs table schema
export const drugs = pgTable(
  'drugs',
  {
    drugId: serial('drug_id').primaryKey().notNull(),
    categoryId: integer('category_id'),
    drugFullName: varchar('drug_full_name', { length: 200 }).notNull(),
    tier: varchar({ length: 200 }).notNull(),
    price: varchar({ length: 200 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    quantity: varchar({ length: 200 })
  },
  (table) => [
    foreignKey({
      columns: [table.categoryId],
      foreignColumns: [table.drugId], // This will be updated to reference drugsCategory.categoryId
      name: 'drugs_category_id_fkey'
    })
  ]
);
