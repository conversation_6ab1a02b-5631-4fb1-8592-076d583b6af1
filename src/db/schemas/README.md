# Database Schema Refactoring

This directory contains the refactored database schemas, organized into individual files per table following the snake_case naming convention to match database table names.

## Structure

### Before Refactoring
- `src/db/migrations/schema.ts` - Monolithic file with all table schemas (4,357 lines)
- `src/db/migrations/relations.ts` - Monolithic file with all table relations (2,012 lines)

### After Refactoring
- `src/db/schemas/[table_name].ts` - Individual files for each table
- `src/db/schemas/index.ts` - Comprehensive re-export file for backward compatibility

## File Naming Convention

Each file is named exactly after its corresponding database table name using snake_case:

- `users.ts` → `users` table
- `consult_notes.ts` → `consult_notes` table
- `telehealth_service_order.ts` → `telehealth_service_order` table
- `drugs_category.ts` → `drugs_category` table

## File Content Structure

Each table file contains:

1. **Imports** - Drizzle ORM imports and utilities
2. **Enums** - Table-specific enums (if any)
3. **Table Schema** - The pgTable definition
4. **Relations** - Relations where this table is the primary/source table (added later)

### Example Structure

```typescript
import {
  pgTable,
  varchar,
  serial,
  // ... other imports
} from 'drizzle-orm/pg-core';

// Table-specific enums
export const enumTableStatus = pgEnum('enum_table_status', [
  'active',
  'inactive'
]);

// Table schema
export const tableName = pgTable(
  'table_name',
  {
    id: serial().primaryKey().notNull(),
    // ... other columns
  },
  (table) => [
    // Foreign key constraints
  ]
);

// Relations (added after all tables are created)
// export const tableNameRelations = relations(tableName, ({ one, many }) => ({
//   // Relations where this table is the source
// }));
```

## Implementation Progress

### Completed Tables
- ✅ `users.ts` - Core user table with enums
- ✅ `orders.ts` - Order management table
- ✅ `consult_notes.ts` - Medical consultation notes
- ✅ `schedules.ts` - Appointment scheduling
- ✅ `pharmacies.ts` - Pharmacy information
- ✅ `states.ts` - US states reference
- ✅ `tennant_master.ts` - Tenant management
- ✅ `telehealth_service_order.ts` - Telehealth service orders
- ✅ `drugs.ts` - Drug information
- ✅ `drugs_category.ts` - Drug categories

### Remaining Tables (130+ total)
- [ ] `auth_provider.ts`
- [ ] `billing.ts`
- [ ] `contact_us.ts`
- [ ] `affiliate_pharmacy.ts`
- [ ] `educational_videos.ts`
- [ ] `encounters_values.ts`
- [ ] `requests.ts`
- [ ] `encryption_keys.ts`
- [ ] `health_summaries_schedule.ts`
- [ ] `precanned_messages.ts`
- [ ] `feed.ts`
- [ ] `forms.ts`
- [ ] `promo_codes.ts`
- [ ] `invitations.ts`
- [ ] `login_requests.ts`
- [ ] `favourite_drugs.ts`
- [ ] ... (and many more)

## Backward Compatibility

The `index.ts` file re-exports all schemas and relations, ensuring existing imports continue to work:

```typescript
// Existing code continues to work
import { users, orders, consultNotes } from '../db/schemas';

// Or with the new structure
import { users } from '../db/schemas/users';
import { orders } from '../db/schemas/orders';
```

## Relations Strategy

Relations are handled carefully to avoid circular imports:

1. **Phase 1**: Create all table schemas without relations
2. **Phase 2**: Add relations to each table file where that table is the primary/source
3. **Phase 3**: Update the index.ts file to export all relations

## Benefits

1. **Maintainability** - Easy to find and modify specific table definitions
2. **Modularity** - Each table is self-contained with its related enums and types
3. **Scalability** - New tables can be added without affecting existing files
4. **Developer Experience** - Faster file navigation and reduced cognitive load
5. **Version Control** - Smaller, focused diffs for table changes
6. **Team Collaboration** - Reduced merge conflicts

## Migration Strategy

1. **Create individual table files** (in progress)
2. **Update foreign key references** to use proper table imports
3. **Add relations** to appropriate table files
4. **Update index.ts** with all exports
5. **Test backward compatibility**
6. **Gradually migrate imports** throughout the codebase (optional)

## Usage Examples

### Importing Specific Tables
```typescript
import { users } from '../db/schemas/users';
import { orders } from '../db/schemas/orders';
```

### Importing Multiple Tables
```typescript
import { users, orders, consultNotes } from '../db/schemas';
```

### Using with Drizzle Queries
```typescript
import { db } from '../db';
import { users } from '../db/schemas/users';
import { eq } from 'drizzle-orm';

const user = await db.select().from(users).where(eq(users.userId, 1));
```

## Next Steps

1. Complete creation of all remaining table files
2. Fix foreign key references between tables
3. Add relations to each table file
4. Update the comprehensive index.ts file
5. Test the refactored structure
6. Update documentation and migration guides
