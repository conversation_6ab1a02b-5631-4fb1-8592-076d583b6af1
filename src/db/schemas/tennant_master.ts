import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  json
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// Tennant Master table schema
export const tennantMaster: any = pgTable(
  'tennant_master',
  {
    id: serial().primaryKey().notNull(),
    tennantName: varchar('tennant_name', { length: 255 }).notNull(),
    pharmacyFax: varchar('pharmacy_fax', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    filterDisplay: boolean('filter_display').default(false),
    parentId: integer('parent_id'),
    labFaxNumber: varchar('lab_fax_number', { length: 255 }),
    supportEmail: varchar('support_email', { length: 255 }),
    logoUrl: varchar('logo_url', { length: 255 }),
    primaryColor: varchar('primary_color', { length: 7 }),
    secondaryColor: varchar('secondary_color', { length: 7 }),
    accentColor: varchar('accent_color', { length: 7 }),
    fontFamily: varchar('font_family', { length: 100 }),
    customCss: text('custom_css'),
    customJs: text('custom_js'),
    favicon: varchar({ length: 255 }),
    metaTitle: varchar('meta_title', { length: 255 }),
    metaDescription: text('meta_description'),
    metaKeywords: text('meta_keywords'),
    googleAnalyticsId: varchar('google_analytics_id', { length: 50 }),
    facebookPixelId: varchar('facebook_pixel_id', { length: 50 }),
    customDomain: varchar('custom_domain', { length: 255 }),
    sslEnabled: boolean('ssl_enabled').default(true),
    maintenanceMode: boolean('maintenance_mode').default(false),
    maintenanceMessage: text('maintenance_message'),
    timezone: varchar({ length: 50 }).default('UTC'),
    locale: varchar({ length: 10 }).default('en'),
    currency: varchar({ length: 3 }).default('USD'),
    dateFormat: varchar('date_format', { length: 20 }).default('MM/DD/YYYY'),
    timeFormat: varchar('time_format', { length: 10 }).default('12'),
    weekStartDay: integer('week_start_day').default(0),
    businessHours: json('business_hours'),
    holidaySchedule: json('holiday_schedule'),
    contactInfo: json('contact_info'),
    socialMedia: json('social_media'),
    integrations: json(),
    features: json(),
    permissions: json(),
    settings: json(),
    branding: json(),
    notifications: json(),
    security: json(),
    compliance: json(),
    billing: json(),
    subscription: json(),
    usage: json(),
    analytics: json(),
    reporting: json(),
    customFields: json('custom_fields'),
    metadata: json(),
    tags: json(),
    notes: text(),
    status: varchar({ length: 50 }).default('active'),
    isActive: boolean('is_active').default(true),
    isVerified: boolean('is_verified').default(false),
    verifiedAt: timestamp('verified_at', { withTimezone: true, mode: 'string' }),
    verifiedBy: integer('verified_by'),
    lastLoginAt: timestamp('last_login_at', { withTimezone: true, mode: 'string' }),
    loginCount: integer('login_count').default(0),
    failedLoginAttempts: integer('failed_login_attempts').default(0),
    lockedAt: timestamp('locked_at', { withTimezone: true, mode: 'string' }),
    passwordChangedAt: timestamp('password_changed_at', { withTimezone: true, mode: 'string' }),
    twoFactorEnabled: boolean('two_factor_enabled').default(false),
    backupCodes: json('backup_codes'),
    apiKey: varchar('api_key', { length: 255 }),
    apiSecret: varchar('api_secret', { length: 255 }),
    webhookUrl: varchar('webhook_url', { length: 500 }),
    webhookSecret: varchar('webhook_secret', { length: 255 }),
    rateLimits: json('rate_limits'),
    ipWhitelist: json('ip_whitelist'),
    ipBlacklist: json('ip_blacklist'),
    allowedOrigins: json('allowed_origins'),
    corsSettings: json('cors_settings'),
    ssoEnabled: boolean('sso_enabled').default(false),
    ssoProvider: varchar('sso_provider', { length: 100 }),
    ssoConfig: json('sso_config'),
    ldapEnabled: boolean('ldap_enabled').default(false),
    ldapConfig: json('ldap_config'),
    auditLog: json('audit_log'),
    backupSettings: json('backup_settings'),
    lastBackupAt: timestamp('last_backup_at', { withTimezone: true, mode: 'string' }),
    storageQuota: integer('storage_quota'),
    storageUsed: integer('storage_used').default(0),
    bandwidthQuota: integer('bandwidth_quota'),
    bandwidthUsed: integer('bandwidth_used').default(0),
    userLimit: integer('user_limit'),
    userCount: integer('user_count').default(0),
    licenseType: varchar('license_type', { length: 100 }),
    licenseKey: varchar('license_key', { length: 255 }),
    licenseExpiry: timestamp('license_expiry', { withTimezone: true, mode: 'string' }),
    supportLevel: varchar('support_level', { length: 50 }),
    supportExpiry: timestamp('support_expiry', { withTimezone: true, mode: 'string' }),
    maintenanceExpiry: timestamp('maintenance_expiry', { withTimezone: true, mode: 'string' }),
    upgradeAvailable: boolean('upgrade_available').default(false),
    upgradeVersion: varchar('upgrade_version', { length: 50 }),
    upgradeNotes: text('upgrade_notes'),
    migrationStatus: varchar('migration_status', { length: 50 }),
    migrationLog: json('migration_log'),
    healthStatus: varchar('health_status', { length: 50 }).default('healthy'),
    healthChecks: json('health_checks'),
    performanceMetrics: json('performance_metrics'),
    errorLog: json('error_log'),
    warningLog: json('warning_log'),
    infoLog: json('info_log'),
    debugLog: json('debug_log'),
    traceLog: json('trace_log')
  },
  (table) => [
    foreignKey({
      columns: [table.parentId],
      foreignColumns: [table.id],
      name: 'tennant_master_parent_id_fkey'
    }),
    foreignKey({
      columns: [table.verifiedBy],
      foreignColumns: [table.id], // This will be updated to reference users.userId
      name: 'tennant_master_verified_by_fkey'
    })
  ]
);
