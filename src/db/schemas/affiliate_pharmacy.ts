import {
  pgTable,
  serial,
  varchar,
  timestamp,
  boolean
} from 'drizzle-orm/pg-core';

// Affiliate Pharmacy table schema
export const affiliatePharmacy = pgTable('affiliate_pharmacy', {
  id: serial().primaryKey().notNull(),
  pharmacyName: varchar('pharmacy_name', { length: 250 }).default(''),
  streetOne: varchar('street_one', { length: 255 }).default(''),
  streetTwo: varchar('street_two', { length: 255 }).default(''),
  city: varchar({ length: 75 }).default(''),
  state: varchar({ length: 50 }).default(''),
  zipCode: varchar('zip_code', { length: 20 }).default(''),
  country: varchar({ length: 50 }).default(''),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  email: varchar({ length: 200 }),
  faxNumber: varchar('fax_number', { length: 20 }),
  phoneNumber: varchar('phone_number', { length: 20 })
});
