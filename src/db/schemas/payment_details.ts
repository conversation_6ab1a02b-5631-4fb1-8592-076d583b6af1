import {
  pgTable,
  serial,
  varchar,
  text,
  timestamp,
  boolean,
  pgEnum
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';
import { transactions } from './transactions';
import { servicePaymentMapping } from '../migrations/schema';

// Enums related to payment_details table
export const enumPaymentDetailsPaymentGateway = pgEnum(
  'enum_payment_details_payment_gateway',
  ['paypal', 'stripe', 'recurly', 'authorize_net', 'no_payment', 'nmi']
);

// Payment Details table schema
export const paymentDetails = pgTable('payment_details', {
  paymentDetailsId: serial('payment_details_id').primaryKey().notNull(),
  paymentGateway: enumPaymentDetailsPaymentGateway(),
  paymentGatewayDetails: text('payment_gateway_details'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' })
});

// Payment Details relations
export const paymentDetailsRelations = relations(
  paymentDetails,
  ({ many }) => ({
    transactions: many(transactions),
    servicePaymentMappings: many(servicePaymentMapping)
  })
);
