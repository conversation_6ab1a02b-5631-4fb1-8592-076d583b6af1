// Re-export all table schemas and relations from individual files
// This file maintains backward compatibility for existing imports

// Core tables (completed)
export * from './users';
export * from './orders';
export * from './consult_notes';
export * from './schedules';
export * from './pharmacies';
export * from './states';
export * from './tennant_master';
export * from './telehealth_service_order';
export * from './drugs';
export * from './drugs_category';

// Additional tables (completed)
export * from './auth_provider';
export * from './billing';
export * from './contact_us';
export * from './affiliate_pharmacy';
export * from './educational_videos';
export * from './encounters_values';
export * from './requests';
export * from './request_objects';
export * from './transactions';

// Additional tables (to be created)
// export * from './encryption_keys';
// export * from './health_summaries_schedule';
// export * from './precanned_messages';
// export * from './feed';
// export * from './forms';
// export * from './promo_codes';
// export * from './drugs';
// export * from './invitations';
// export * from './login_requests';
// export * from './favourite_drugs';
// export * from './micromerchant_users';
// export * from './procedure_codes';
// export * from './immunizations_values';
// export * from './medications_values';
// export * from './mms_patients';
// export * from './mms_request_payload';
// export * from './mms_prescriptions';
// export * from './onehealth_lab_orders';
// export * from './patient_insurances';
// export * from './permission_groups';
// export * from './practice_groups';
// export * from './mobile_number_otp_validator';
// export * from './consult_update_details_history';
// export * from './provider_license';
// export * from './requests_log';
// export * from './request_objects';
// export * from './medicines';
// export * from './medicine_service_pharmacy_mapping';
// export * from './chat_rooms';
// export * from './chat_room_members';
// export * from './subscription_plans';
// export * from './special_discounts';
// export * from './stripe_user_payment_details';
// export * from './telehealth_service_procedure_codes_mapping';
// export * from './telehealth_service_questions';
// export * from './telehealth_service_provider_mapping';
// export * from './transactions';
// export * from './stripe_user_details';
// export * from './social_history_values';
// export * from './user_particlehealth';
// export * from './user_details';
// export * from './user_educational_videos';
// export * from './products';
// export * from './user_identities';
// export * from './follow_up_reminder';
// export * from './user_file_repo_details';
// export * from './user_health_summary';
// export * from './user_subscription_billing';
// export * from './drug_days';
// export * from './user_subscription';
// export * from './consult_order_files';
// export * from './sms_template';
// export * from './user_vitals_documents';
// export * from './chat_messages';
// export * from './jobs';
// export * from './user_schedules';
// export * from './chat_files';
// export * from './email_template';
// export * from './prescription_preference';
// export * from './lifefile_configuration';
// export * from './telehealth_service_question_answer_dump';
// export * from './consult_reassign_history';
// export * from './external_requests_log';
// export * from './mms_patient_invitations';
// export * from './service_payment_mapping';
// export * from './referral_tracking';
// export * from './support_notes';
// export * from './telehealth_services';
// export * from './tennant_config';
// export * from './telehealth_service_master';
// export * from './tenant_auth_provider';
// export * from './service_action_preference';
// export * from './diagnoses_values';
// export * from './family_history_values';
// export * from './procedures_values';
// export * from './care_plan_values';
// export * from './results_values';
// export * from './allergies_values';
// export * from './document_values';
// export * from './mms_prescription_refills';
// export * from './vitals_summary_upload_status';
// export * from './vitals_notification_cycle';
// export * from './visit_summary_upload_status';
// export * from './vitals_patient_monitoring';
// export * from './user_forms';
// export * from './user_diet';
// export * from './telehealth_service_question_answer';
// export * from './faxes';
// export * from './reviews';
// export * from './referrals';
// export * from './transcriptions';
// export * from './user_vitals';
// export * from './provider_ratings';
// export * from './tokbox_archive_type';
// export * from './health_summary_metadata';
// export * from './health_summaries_log';
// export * from './conversation_messages';
// export * from './conversations';
// export * from './prescription_transfer_medications';
// export * from './prescription_transfer_request';
// export * from './refill_request';
// export * from './telehealth_service_state_mapping';
// export * from './drugs_category';
// export * from './webhooks_log';
// export * from './payment_details';
// export * from './pharmacy_state_service_mapping';
// export * from './user_insurance';
// export * from './insurance_eligibility_logs';
// export * from './user_external_id_mapping';
// export * from './user_files';
// export * from './consult_note_templates';
// export * from './tenant_files';
// export * from './telehealth_service_category';
// export * from './user_licenses';
// export * from './notification_reminder';
// export * from './user_practice_groups';
// export * from './schedule_translation';
// export * from './request_translation';
// export * from './users_translation';
// export * from './user_viewers';
// export * from './user_associations';

// Views and materialized views
// export * from './dashboard_tennant_list';
// export * from './dashboard_tennant_list_mv';

// Legacy exports for backward compatibility
export * from './encrypted-users';

// Re-export common Drizzle ORM types and utilities
export { relations } from 'drizzle-orm/relations';
export { sql } from 'drizzle-orm';
export type { InferModel } from 'drizzle-orm';
