import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  numeric,
  json
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// Pharmacies table schema
export const pharmacies = pgTable(
  'pharmacies',
  {
    pharmacyId: serial('pharmacy_id').primaryKey().notNull(),
    npi: varchar({ length: 255 }),
    name: varchar({ length: 255 }),
    nabp: varchar({ length: 255 }),
    address: text(),
    phone: varchar({ length: 255 }),
    fax: varchar({ length: 255 }),
    email: varchar({ length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    ncpdpid: varchar({ length: 50 }),
    pharmcistName: varchar('pharmcist_name', { length: 50 }),
    dea: varchar({ length: 50 }),
    pharmacyLegalName: varchar('pharmacy_legal_name', { length: 100 }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    city: varchar({ length: 100 }),
    state: varchar({ length: 50 }),
    zipCode: varchar('zip_code', { length: 20 }),
    country: varchar({ length: 50 }).default('USA'),
    latitude: numeric({ precision: 10, scale: 8 }),
    longitude: numeric({ precision: 11, scale: 8 }),
    timezone: varchar({ length: 50 }),
    operatingHours: json('operating_hours'),
    services: json(),
    specialties: json(),
    certifications: json(),
    insuranceAccepted: json('insurance_accepted'),
    deliveryOptions: json('delivery_options'),
    website: varchar({ length: 255 }),
    socialMedia: json('social_media'),
    rating: numeric({ precision: 3, scale: 2 }),
    reviewCount: integer('review_count').default(0),
    isActive: boolean('is_active').default(true),
    isVerified: boolean('is_verified').default(false),
    verifiedAt: timestamp('verified_at', { withTimezone: true, mode: 'string' }),
    verifiedBy: integer('verified_by'),
    licenseNumber: varchar('license_number', { length: 100 }),
    licenseState: varchar('license_state', { length: 50 }),
    licenseExpiry: timestamp('license_expiry', { withTimezone: true, mode: 'string' }),
    complianceStatus: varchar('compliance_status', { length: 50 }),
    lastInspection: timestamp('last_inspection', { withTimezone: true, mode: 'string' }),
    nextInspection: timestamp('next_inspection', { withTimezone: true, mode: 'string' }),
    accreditation: json(),
    emergencyContact: json('emergency_contact'),
    backupPharmacy: integer('backup_pharmacy'),
    chainId: varchar('chain_id', { length: 100 }),
    franchiseInfo: json('franchise_info'),
    paymentMethods: json('payment_methods'),
    shippingMethods: json('shipping_methods'),
    returnPolicy: text('return_policy'),
    privacyPolicy: text('privacy_policy'),
    termsOfService: text('terms_of_service'),
    apiCredentials: json('api_credentials'),
    integrationSettings: json('integration_settings'),
    notificationPreferences: json('notification_preferences'),
    reportingSettings: json('reporting_settings'),
    auditLog: json('audit_log'),
    metadata: json(),
    tags: json(),
    notes: text(),
    internalId: varchar('internal_id', { length: 100 }),
    externalId: varchar('external_id', { length: 100 }),
    syncStatus: varchar('sync_status', { length: 50 }),
    lastSyncAt: timestamp('last_sync_at', { withTimezone: true, mode: 'string' }),
    dataSource: varchar('data_source', { length: 100 }),
    dataQuality: integer('data_quality'),
    isTestPharmacy: boolean('is_test_pharmacy').default(false),
    maintenanceMode: boolean('maintenance_mode').default(false),
    maintenanceMessage: text('maintenance_message'),
    featuredPharmacy: boolean('featured_pharmacy').default(false),
    promotionalOffers: json('promotional_offers'),
    loyaltyProgram: json('loyalty_program'),
    customerSupport: json('customer_support'),
    businessHours: json('business_hours'),
    holidaySchedule: json('holiday_schedule'),
    emergencyServices: boolean('emergency_services').default(false),
    consultationServices: boolean('consultation_services').default(false),
    vaccinationServices: boolean('vaccination_services').default(false),
    healthScreenings: boolean('health_screenings').default(false),
    compoundingServices: boolean('compounding_services').default(false),
    medicalEquipment: boolean('medical_equipment').default(false),
    homeDelivery: boolean('home_delivery').default(false),
    curbsidePickup: boolean('curbside_pickup').default(false),
    driveThrough: boolean('drive_through').default(false),
    onlineOrdering: boolean('online_ordering').default(false),
    mobileApp: boolean('mobile_app').default(false),
    autoRefill: boolean('auto_refill').default(false),
    textReminders: boolean('text_reminders').default(false),
    emailReminders: boolean('email_reminders').default(false),
    multilingual: boolean().default(false),
    languagesSupported: json('languages_supported'),
    accessibilityFeatures: json('accessibility_features'),
    parkingAvailable: boolean('parking_available').default(false),
    publicTransportAccess: boolean('public_transport_access').default(false),
    wheelchairAccessible: boolean('wheelchair_accessible').default(false)
  },
  (table) => [
    foreignKey({
      columns: [table.verifiedBy],
      foreignColumns: [table.pharmacyId], // This will be updated to reference users.userId
      name: 'pharmacies_verified_by_fkey'
    }),
    foreignKey({
      columns: [table.backupPharmacy],
      foreignColumns: [table.pharmacyId],
      name: 'pharmacies_backup_pharmacy_fkey'
    })
  ]
);
