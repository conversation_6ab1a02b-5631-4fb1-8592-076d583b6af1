import {
  pgTable,
  serial,
  varchar,
  timestamp
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// Request Objects table schema
export const requestObjects = pgTable('request_objects', {
  objectId: serial('object_id').primaryKey().notNull(),
  objectName: varchar('object_name', { length: 255 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

// Request Objects relations
export const requestObjectsRelations = relations(requestObjects, ({ many }) => ({
  requests: many(requests)
}));
