import {
  pgTable,
  serial,
  integer,
  varchar,
  timestamp
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';
import { users } from './users';

// Contact Us table schema
export const contactUs = pgTable('contact_us', {
  contactUsId: serial('contact_us_id').primaryKey().notNull(),
  userId: integer('user_id').notNull(),
  dateRequested: timestamp('date_requested', {
    withTimezone: true,
    mode: 'string'
  }),
  title: varchar({ length: 1000 }).default('').notNull(),
  question: varchar({ length: 1000 }).default('').notNull(),
  queryText: varchar({ length: 4000 }).default('').notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});

// Contact Us relations
export const contactUsRelations = relations(contactUs, ({ one }) => ({
  user: one(users, {
    fields: [contactUs.userId],
    references: [users.userId]
  })
}));
