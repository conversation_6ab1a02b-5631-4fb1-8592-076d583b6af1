import {
  pgTable,
  foreignKey,
  serial,
  integer,
  text,
  timestamp
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';
import { users } from './users';

// Educational Videos table schema
export const educationalVideos = pgTable(
  'educational_videos',
  {
    videoId: serial('video_id').primaryKey().notNull(),
    sessionId: text('session_id'),
    archiveId: text('archive_id'),
    title: text().notNull(),
    description: text(),
    url: text(),
    createdBy: integer('created_by'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    audioStreamScreenshot: text('audio_stream_screenshot')
  },
  (table) => [
    foreignKey({
      columns: [table.createdBy],
      foreignColumns: [table.videoId], // Will be updated to reference users.userId
      name: 'educational_videos_created_by_fkey'
    })
  ]
);

// Educational Videos relations
export const educationalVideosRelations = relations(
  educationalVideos,
  ({ one }) => ({
    createdByUser: one(users, {
      fields: [educationalVideos.createdBy],
      references: [users.userId]
    })
  })
);
