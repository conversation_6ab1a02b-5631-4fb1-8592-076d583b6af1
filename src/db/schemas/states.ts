import {
  pgTable,
  varchar,
  serial,
  timestamp,
  boolean
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// States table schema
export const states = pgTable('states', {
  stateId: serial('state_id').primaryKey().notNull(),
  name: varchar({ length: 50 }).notNull(),
  abbreviation: varchar({ length: 2 }).notNull(),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
  deleted: boolean().default(false),
  deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
  isAsync: boolean('is_async').default(true).notNull()
});
