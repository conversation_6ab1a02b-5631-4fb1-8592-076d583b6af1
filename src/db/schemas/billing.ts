import { pgTable, serial, text, timestamp } from 'drizzle-orm/pg-core';

// Billing table schema
export const billing = pgTable('billing', {
  planId: serial('plan_id').notNull(),
  planName: text('plan_name'),
  planDescription: text('plan_description'),
  createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
  updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' })
});
