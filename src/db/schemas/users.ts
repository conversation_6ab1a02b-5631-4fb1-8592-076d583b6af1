import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  unique,
  pgEnum
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

// Enums related to users table
export const enumUsersRole = pgEnum('enum_users_role', [
  'USER',
  'DOCTOR',
  'ADMIN',
  'SUPER_ADMIN',
  'PHARMACY',
  'NURSE',
  'PATIENT',
  'PROVIDER',
  'SUPPORT',
  'TENANT_ADMIN'
]);

export const enumUsersStatus = pgEnum('enum_users_status', [
  'ONLINE',
  'OFFLINE',
  'BUSY',
  'AWAY'
]);

export const enumUsersGender = pgEnum('enum_users_gender', [
  'male',
  'female',
  'other'
]);

// Users table schema
export const users = pgTable(
  'users',
  {
    userId: serial('user_id').primaryKey().notNull(),
    userGuid: varchar('user_guid', { length: 255 }),
    password: text(),
    role: enumUsersRole().default('USER'),
    status: enumUsersStatus().default('OFFLINE'),
    installType: varchar('install_type', { length: 255 }),
    firstName: text('first_name'),
    lastName: text('last_name'),
    zipCode: varchar('zip_code', { length: 255 }),
    email: text().notNull(),
    phone: text(),
    otp: text(),
    dob: text(),
    apptLength: integer('appt_length'),
    apptStartTime: varchar('appt_start_time', { length: 255 }),
    apptEndTime: varchar('appt_end_time', { length: 255 }),
    secureMessage: boolean('secure_message').default(false),
    connectionRequests: boolean('connection_requests').default(false),
    vitalsCcdEnabled: boolean('vitals_ccd_enabled').default(false),
    apptRequests: boolean('appt_requests').default(false),
    trialValidity: timestamp('trial_validity', {
      withTimezone: true,
      mode: 'string'
    }),
    isCcCaptured: boolean('is_cc_captured'),
    gender: enumUsersGender().default('male'),
    deleted: boolean().default(false),
    emailVerified: boolean('email_verified').default(false),
    userAvatar: text('user_avatar'),
    idCard: text('id_card'),
    history: text(),
    questionnaire: text(),
    tokenValidity: integer('token_validity'),
    emailVerificationDetails: text('email_verification_details'),
    lastActive: timestamp('last_active', {
      withTimezone: true,
      mode: 'string'
    }),
    myInviteCode: varchar('my_invite_code', { length: 255 })
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    referredByInviteCode: varchar('referred_by_invite_code', { length: 255 })
      .default(sql`uuid_generate_v4()`)
      .notNull(),
    debug: boolean().default(false),
    invitationCodeValidity: integer('invitation_code_validity'),
    cronExpression: text('cron_expression'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    mirthCcdEnabled: boolean('mirth_ccd_enabled').default(false),
    ccPaymentAccepted: boolean('cc_payment_accepted').default(false),
    recordingEnabled: boolean('recording_enabled').default(true),
    transcriptionEnabled: boolean('transcription_enabled').default(true),
    ordersEnabled: boolean('orders_enabled').default(true),
    showHealthSummaries: boolean('show_health_summaries'),
    healthgorillaId: varchar('healthgorilla_id', { length: 255 }),
    releaseMedical: boolean('release_medical'),
    npi: varchar({ length: 255 }),
    dea: varchar({ length: 255 }),
    licenseNumber: varchar('license_number', { length: 255 }),
    licenseState: varchar('license_state', { length: 255 }),
    licenseExpiry: timestamp('license_expiry', {
      withTimezone: true,
      mode: 'string'
    }),
    tennantId: integer('tennant_id'),
    tenantAccess: varchar('tenant_access', { length: 500 }),
    dosespotApiResponse: varchar('dosespot_api_response', { length: 2000 }),
    idCardFile: text('id_card_file'),
    pharmacyAccess: varchar('pharmacy_access', { length: 500 }),
    parentId: integer('parent_id'),
    dependentAccountRelation: varchar('dependent_account_relation', {
      length: 255
    }),
    trackingCode: varchar('tracking_code', { length: 255 }),
    userPrivateKey: text('user_private_key')
  },
  (table) => [
    foreignKey({
      columns: [table.tennantId],
      foreignColumns: [table.userId], // This will be updated when tennantMaster is created
      name: 'users_tennant_id_fkey'
    }),
    foreignKey({
      columns: [table.parentId],
      foreignColumns: [table.userId],
      name: 'users_parent_id_fkey'
    }),
    unique('users_my_invite_code_key').on(table.myInviteCode)
  ]
);

// Users relations will be added after all tables are created
// This prevents circular import issues during the initial setup
