import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  pgEnum
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';
import { sql } from 'drizzle-orm';

// Enums related to consult_notes table
export const enumConsultNotesStatus = pgEnum('enum_consult_notes_status', [
  'draft',
  'completed',
  'reviewed',
  'signed'
]);

// Consult Notes table schema
export const consultNotes = pgTable(
  'consult_notes',
  {
    consultNoteId: serial('consult_note_id').primaryKey().notNull(),
    orderId: text('order_id'),
    doctorId: integer('doctor_id'),
    patientId: integer('patient_id'),
    chiefComplaint: text('chief_complaint'),
    historyOfPresentIllness: text('history_of_present_illness'),
    reviewOfSystems: text('review_of_systems'),
    pastMedicalHistory: text('past_medical_history'),
    medications: text(),
    allergies: text(),
    socialHistory: text('social_history'),
    familyHistory: text('family_history'),
    physicalExam: text('physical_exam'),
    assessment: text(),
    plan: text(),
    followUp: text('follow_up'),
    generalNote: text('general_note').default(sql`''`).notNull(),
    status: enumConsultNotesStatus().default('draft'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    signedAt: timestamp('signed_at', { withTimezone: true, mode: 'string' }),
    signedBy: integer('signed_by'),
    reviewedAt: timestamp('reviewed_at', { withTimezone: true, mode: 'string' }),
    reviewedBy: integer('reviewed_by'),
    templateId: integer('template_id'),
    diagnosis: text(),
    procedures: text(),
    prescriptions: text(),
    labOrders: text('lab_orders'),
    imagingOrders: text('imaging_orders'),
    referrals: text(),
    vitalSigns: text('vital_signs'),
    immunizations: text(),
    screenings: text(),
    counseling: text(),
    patientEducation: text('patient_education'),
    nextAppointment: text('next_appointment'),
    billingCodes: text('billing_codes'),
    notes: text(),
    metadata: text(),
    externalNoteId: varchar('external_note_id', { length: 255 }),
    source: varchar({ length: 255 }),
    version: integer().default(1),
    parentNoteId: integer('parent_note_id'),
    isAmendment: boolean('is_amendment').default(false),
    amendmentReason: text('amendment_reason')
  },
  (table) => [
    foreignKey({
      columns: [table.orderId],
      foreignColumns: [table.consultNoteId], // This will be updated to reference orders.orderId
      name: 'consult_notes_order_id_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [table.consultNoteId], // This will be updated to reference users.userId
      name: 'consult_notes_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.patientId],
      foreignColumns: [table.consultNoteId], // This will be updated to reference users.userId
      name: 'consult_notes_patient_id_fkey'
    }),
    foreignKey({
      columns: [table.signedBy],
      foreignColumns: [table.consultNoteId], // This will be updated to reference users.userId
      name: 'consult_notes_signed_by_fkey'
    }),
    foreignKey({
      columns: [table.reviewedBy],
      foreignColumns: [table.consultNoteId], // This will be updated to reference users.userId
      name: 'consult_notes_reviewed_by_fkey'
    }),
    foreignKey({
      columns: [table.parentNoteId],
      foreignColumns: [table.consultNoteId],
      name: 'consult_notes_parent_note_id_fkey'
    })
  ]
);
