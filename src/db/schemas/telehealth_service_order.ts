import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  numeric,
  json,
  pgEnum
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';
import { sql } from 'drizzle-orm';

// Enums related to telehealth_service_order table
export const enumTelehealthServiceOrderStatus = pgEnum(
  'enum_telehealth_service_order_status',
  [
    'pending',
    'in_progress',
    'completed',
    'cancelled',
    'failed',
    'refunded',
    'expired'
  ]
);

export const enumTelehealthServiceOrderType = pgEnum(
  'enum_telehealth_service_order_type',
  ['consultation', 'prescription', 'lab_order', 'follow_up', 'emergency']
);

// Telehealth Service Order table schema
export const telehealthServiceOrder: any = pgTable(
  'telehealth_service_order',
  {
    id: serial().primaryKey().notNull(),
    userId: integer('user_id'),
    serviceId: integer('service_id'),
    doctorId: integer('doctor_id'),
    pharmacyId: integer('pharmacy_id'),
    status: enumTelehealthServiceOrderStatus().default('pending'),
    type: enumTelehealthServiceOrderType().default('consultation'),
    orderGuid: text('order_guid').default(sql`uuid_generate_v4()`).notNull(),
    cost: numeric(),
    currency: varchar({ length: 3 }).default('USD'),
    paymentStatus: varchar('payment_status', { length: 50 }),
    paymentMethod: varchar('payment_method', { length: 50 }),
    transactionId: varchar('transaction_id', { length: 255 }),
    refundAmount: numeric('refund_amount'),
    refundReason: text('refund_reason'),
    scheduledAt: timestamp('scheduled_at', { withTimezone: true, mode: 'string' }),
    startedAt: timestamp('started_at', { withTimezone: true, mode: 'string' }),
    completedAt: timestamp('completed_at', { withTimezone: true, mode: 'string' }),
    cancelledAt: timestamp('cancelled_at', { withTimezone: true, mode: 'string' }),
    cancelReason: text('cancel_reason'),
    notes: text(),
    metadata: json(),
    questionnaire: json(),
    symptoms: text(),
    diagnosis: text(),
    prescription: text(),
    labOrders: text('lab_orders'),
    followUpRequired: boolean('follow_up_required').default(false),
    followUpDate: timestamp('follow_up_date', { withTimezone: true, mode: 'string' }),
    rating: integer(),
    feedback: text(),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    externalOrderId: varchar('external_order_id', { length: 255 }),
    source: varchar({ length: 100 }),
    priority: varchar({ length: 50 }).default('normal'),
    estimatedDuration: integer('estimated_duration'),
    actualDuration: integer('actual_duration'),
    sessionId: varchar('session_id', { length: 255 }),
    recordingUrl: text('recording_url'),
    transcriptionUrl: text('transcription_url'),
    documentUrls: json('document_urls'),
    insuranceInfo: json('insurance_info'),
    billingAddress: json('billing_address'),
    shippingAddress: json('shipping_address'),
    promoCode: varchar('promo_code', { length: 100 }),
    discountAmount: numeric('discount_amount'),
    taxAmount: numeric('tax_amount'),
    totalAmount: numeric('total_amount'),
    patientConsent: boolean('patient_consent').default(false),
    providerNotes: text('provider_notes'),
    patientInstructions: text('patient_instructions'),
    nextSteps: text('next_steps'),
    referralRequired: boolean('referral_required').default(false),
    referralDetails: text('referral_details'),
    emergencyContact: json('emergency_contact'),
    allergies: text(),
    currentMedications: text('current_medications'),
    medicalHistory: text('medical_history'),
    vitalSigns: json('vital_signs'),
    labResults: json('lab_results'),
    imagingResults: json('imaging_results'),
    treatmentPlan: text('treatment_plan'),
    dischargeSummary: text('discharge_summary'),
    qualityScore: integer('quality_score'),
    complianceFlags: json('compliance_flags'),
    auditTrail: json('audit_trail')
  },
  (table) => [
    foreignKey({
      columns: [table.userId],
      foreignColumns: [table.id], // This will be updated to reference users.userId
      name: 'telehealth_service_order_user_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [table.id], // This will be updated to reference telehealthServices.id
      name: 'telehealth_service_order_service_id_fkey'
    }),
    foreignKey({
      columns: [table.doctorId],
      foreignColumns: [table.id], // This will be updated to reference users.userId
      name: 'telehealth_service_order_doctor_id_fkey'
    }),
    foreignKey({
      columns: [table.pharmacyId],
      foreignColumns: [table.id], // This will be updated to reference pharmacies.pharmacyId
      name: 'telehealth_service_order_pharmacy_id_fkey'
    })
  ]
);
