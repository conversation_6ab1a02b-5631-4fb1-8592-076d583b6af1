import {
  pgTable,
  varchar,
  foreignKey,
  serial,
  integer,
  text,
  timestamp,
  boolean,
  json,
  pgEnum
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm/relations';

// Enums related to schedules table
export const enumSchedulesStatus = pgEnum('enum_schedules_status', [
  'scheduled',
  'confirmed',
  'in_progress',
  'completed',
  'cancelled',
  'no_show',
  'rescheduled'
]);

export const enumSchedulesType = pgEnum('enum_schedules_type', [
  'consultation',
  'follow_up',
  'emergency',
  'routine_checkup',
  'procedure',
  'lab_work',
  'imaging'
]);

// Schedules table schema
export const schedules = pgTable(
  'schedules',
  {
    scheduleId: serial('schedule_id').primaryKey().notNull(),
    scheduledWith: integer('scheduled_with'),
    scheduledBy: integer('scheduled_by'),
    startYear: integer('start_year').notNull(),
    startMonth: integer('start_month').notNull(),
    startDay: integer('start_day').notNull(),
    endYear: integer('end_year').notNull(),
    endMonth: integer('end_month').notNull(),
    endDay: integer('end_day').notNull(),
    start: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    end: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    detail: text(),
    patientHistory: text('patient_history'),
    createdAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),
    updatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),
    deletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),
    deleted: boolean().default(false),
    orderGuid: text('order_guid'),
    status: enumSchedulesStatus().default('scheduled'),
    type: enumSchedulesType().default('consultation'),
    title: varchar({ length: 255 }),
    description: text(),
    location: varchar({ length: 255 }),
    meetingLink: varchar('meeting_link', { length: 500 }),
    meetingId: varchar('meeting_id', { length: 255 }),
    meetingPassword: varchar('meeting_password', { length: 255 }),
    timezone: varchar({ length: 50 }),
    duration: integer(), // in minutes
    bufferTime: integer('buffer_time'), // in minutes
    reminderSent: boolean('reminder_sent').default(false),
    reminderTime: timestamp('reminder_time', {
      withTimezone: true,
      mode: 'string'
    }),
    confirmationRequired: boolean('confirmation_required').default(true),
    confirmedAt: timestamp('confirmed_at', {
      withTimezone: true,
      mode: 'string'
    }),
    confirmedBy: integer('confirmed_by'),
    cancelledAt: timestamp('cancelled_at', {
      withTimezone: true,
      mode: 'string'
    }),
    cancelledBy: integer('cancelled_by'),
    cancelReason: text('cancel_reason'),
    rescheduledFrom: integer('rescheduled_from'),
    rescheduledTo: integer('rescheduled_to'),
    rescheduleReason: text('reschedule_reason'),
    noShowAt: timestamp('no_show_at', { withTimezone: true, mode: 'string' }),
    noShowReason: text('no_show_reason'),
    checkedInAt: timestamp('checked_in_at', {
      withTimezone: true,
      mode: 'string'
    }),
    checkedOutAt: timestamp('checked_out_at', {
      withTimezone: true,
      mode: 'string'
    }),
    actualStartTime: timestamp('actual_start_time', {
      withTimezone: true,
      mode: 'string'
    }),
    actualEndTime: timestamp('actual_end_time', {
      withTimezone: true,
      mode: 'string'
    }),
    waitTime: integer('wait_time'), // in minutes
    serviceTime: integer('service_time'), // in minutes
    priority: varchar({ length: 50 }).default('normal'),
    urgency: varchar({ length: 50 }).default('routine'),
    specialInstructions: text('special_instructions'),
    preparationInstructions: text('preparation_instructions'),
    followUpRequired: boolean('follow_up_required').default(false),
    followUpDate: timestamp('follow_up_date', {
      withTimezone: true,
      mode: 'string'
    }),
    recurringSchedule: json('recurring_schedule'),
    isRecurring: boolean('is_recurring').default(false),
    parentScheduleId: integer('parent_schedule_id'),
    seriesId: varchar('series_id', { length: 255 }),
    attendees: json(),
    resources: json(),
    equipment: json(),
    room: varchar({ length: 100 }),
    department: varchar({ length: 100 }),
    serviceId: integer('service_id'),
    providerId: integer('provider_id'),
    facilityId: integer('facility_id'),
    insuranceInfo: json('insurance_info'),
    copayAmount: integer('copay_amount'),
    estimatedCost: integer('estimated_cost'),
    actualCost: integer('actual_cost'),
    billingCode: varchar('billing_code', { length: 100 }),
    diagnosisCode: varchar('diagnosis_code', { length: 100 }),
    procedureCode: varchar('procedure_code', { length: 100 }),
    referralRequired: boolean('referral_required').default(false),
    referralNumber: varchar('referral_number', { length: 100 }),
    authorizationRequired: boolean('authorization_required').default(false),
    authorizationNumber: varchar('authorization_number', { length: 100 }),
    preAuthRequired: boolean('pre_auth_required').default(false),
    preAuthNumber: varchar('pre_auth_number', { length: 100 }),
    eligibilityVerified: boolean('eligibility_verified').default(false),
    eligibilityVerifiedAt: timestamp('eligibility_verified_at', {
      withTimezone: true,
      mode: 'string'
    }),
    benefitsVerified: boolean('benefits_verified').default(false),
    benefitsVerifiedAt: timestamp('benefits_verified_at', {
      withTimezone: true,
      mode: 'string'
    }),
    consentObtained: boolean('consent_obtained').default(false),
    consentObtainedAt: timestamp('consent_obtained_at', {
      withTimezone: true,
      mode: 'string'
    }),
    documentsRequired: json('documents_required'),
    documentsReceived: json('documents_received'),
    labOrdersRequired: json('lab_orders_required'),
    imagingRequired: json('imaging_required'),
    medicationsRequired: json('medications_required'),
    allergies: text(),
    medicalHistory: text('medical_history'),
    currentMedications: text('current_medications'),
    vitalSigns: json('vital_signs'),
    chiefComplaint: text('chief_complaint'),
    symptoms: text(),
    assessment: text(),
    plan: text(),
    notes: text(),
    internalNotes: text('internal_notes'),
    publicNotes: text('public_notes'),
    tags: json(),
    metadata: json(),
    customFields: json('custom_fields'),
    externalId: varchar('external_id', { length: 255 }),
    source: varchar({ length: 100 }),
    syncStatus: varchar('sync_status', { length: 50 }),
    lastSyncAt: timestamp('last_sync_at', {
      withTimezone: true,
      mode: 'string'
    }),
    qualityScore: integer('quality_score'),
    satisfactionRating: integer('satisfaction_rating'),
    feedback: text(),
    auditTrail: json('audit_trail'),
    complianceFlags: json('compliance_flags'),
    emergencyContact: json('emergency_contact'),
    interpreterRequired: boolean('interpreter_required').default(false),
    interpreterLanguage: varchar('interpreter_language', { length: 100 }),
    accessibilityNeeds: json('accessibility_needs'),
    communicationPreferences: json('communication_preferences'),
    notificationPreferences: json('notification_preferences'),
    consentHistory: json('consent_history'),
    formsCompleted: json('forms_completed'),
    careCoordination: json('care_coordination'),
    referralsGiven: json('referrals_given'),
    prescriptionsGiven: json('prescriptions_given'),
    labOrdersGiven: json('lab_orders_given'),
    proceduresPerformed: json('procedures_performed'),
    treatmentOutcomes: json('treatment_outcomes'),
    complications: json(),
    qualityMetrics: json('quality_metrics'),
    performanceMetrics: json('performance_metrics')
  },
  (table) => [
    foreignKey({
      columns: [table.scheduledWith],
      foreignColumns: [table.scheduleId], // This will be updated to reference users.userId
      name: 'schedules_scheduled_with_fkey'
    }),
    foreignKey({
      columns: [table.scheduledBy],
      foreignColumns: [table.scheduleId], // This will be updated to reference users.userId
      name: 'schedules_scheduled_by_fkey'
    }),
    foreignKey({
      columns: [table.confirmedBy],
      foreignColumns: [table.scheduleId], // This will be updated to reference users.userId
      name: 'schedules_confirmed_by_fkey'
    }),
    foreignKey({
      columns: [table.cancelledBy],
      foreignColumns: [table.scheduleId], // This will be updated to reference users.userId
      name: 'schedules_cancelled_by_fkey'
    }),
    foreignKey({
      columns: [table.rescheduledFrom],
      foreignColumns: [table.scheduleId],
      name: 'schedules_rescheduled_from_fkey'
    }),
    foreignKey({
      columns: [table.rescheduledTo],
      foreignColumns: [table.scheduleId],
      name: 'schedules_rescheduled_to_fkey'
    }),
    foreignKey({
      columns: [table.parentScheduleId],
      foreignColumns: [table.scheduleId],
      name: 'schedules_parent_schedule_id_fkey'
    }),
    foreignKey({
      columns: [table.serviceId],
      foreignColumns: [table.scheduleId], // This will be updated to reference telehealthServices.id
      name: 'schedules_service_id_fkey'
    }),
    foreignKey({
      columns: [table.providerId],
      foreignColumns: [table.scheduleId], // This will be updated to reference users.userId
      name: 'schedules_provider_id_fkey'
    }),
    foreignKey({
      columns: [table.facilityId],
      foreignColumns: [table.scheduleId], // This will be updated to reference facilities.id
      name: 'schedules_facility_id_fkey'
    })
  ]
);
