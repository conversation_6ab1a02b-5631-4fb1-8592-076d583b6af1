-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."enum_consult_order_files_type" AS ENUM('audio', 'video', 'file', 'image');--> statement-breakpoint
CREATE TYPE "public"."enum_follow_up_reminder_status" AS ENUM('pending', 'started', 'failed', 'sent', 'cancel', 'cancelled_by_admin');--> statement-breakpoint
CREATE TYPE "public"."enum_jobs_status" AS ENUM('pending', 'started', 'failed', 'sending_fax', 'completed');--> statement-breakpoint
CREATE TYPE "public"."enum_lifefile_configuration_shipping_services" AS ENUM('7780', '9');--> statement-breakpoint
CREATE TYPE "public"."enum_onehealth_lab_orders_payment_status" AS ENUM('PENDING', 'FAILED', 'PAID');--> statement-breakpoint
CREATE TYPE "public"."enum_onehealth_lab_orders_status" AS ENUM('PENDING', 'PAYMENT_PENDING', 'COMPLETE_COLLECTION', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."enum_orders_category" AS ENUM('CCM', 'RPM', 'BHI');--> statement-breakpoint
CREATE TYPE "public"."enum_orders_status" AS ENUM('STARTED', 'INPROGRESS', 'ENDED', 'NOANSWER', 'REJECTED', 'QUEUED', 'RINGING', 'CANCELLED', 'COMPLETED', 'BUSY', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."enum_orders_type" AS ENUM('audio', 'video', 'one-way', 'online', 'voip');--> statement-breakpoint
CREATE TYPE "public"."enum_payment_details_payment_gateway" AS ENUM('paypal', 'stripe', 'recurly', 'authorize_net');--> statement-breakpoint
CREATE TYPE "public"."enum_prescription_preference_preference" AS ENUM('fax', 'life_file', 'doespot');--> statement-breakpoint
CREATE TYPE "public"."enum_prescription_transfer_medications_status" AS ENUM('pending', 'transferred');--> statement-breakpoint
CREATE TYPE "public"."enum_promo_codes_code_type" AS ENUM('PROMO CODE', 'INVITE PROMO CODE', 'MD', 'LIFESTYLE', 'LAB', 'MD/LIFESTYLE/LAB');--> statement-breakpoint
CREATE TYPE "public"."enum_promo_codes_discount_type" AS ENUM('PERCENTAGE', 'DOLLAR');--> statement-breakpoint
CREATE TYPE "public"."enum_promo_codes_usage_type" AS ENUM('SINGLE', 'MULTIPLE');--> statement-breakpoint
CREATE TYPE "public"."enum_referral_tracking_shipping_partner" AS ENUM('UPS', 'USPS', 'FedEx');--> statement-breakpoint
CREATE TYPE "public"."enum_referrals_status" AS ENUM('issued', 'canceled', 'pdf_pending');--> statement-breakpoint
CREATE TYPE "public"."enum_requests_status" AS ENUM('open', 'accepted', 'rejected');--> statement-breakpoint
CREATE TYPE "public"."enum_service_payment_mapping_payment_type" AS ENUM('subscription', 'one-time', 'one-time-insurance');--> statement-breakpoint
CREATE TYPE "public"."enum_stripe_user_details_cc_status" AS ENUM('captured', 'not_captured', 'payment_error', 'capture_immediate');--> statement-breakpoint
CREATE TYPE "public"."enum_stripe_user_details_oauth_status" AS ENUM('connected', 'not_connected', 'payouts_disabled');--> statement-breakpoint
CREATE TYPE "public"."enum_stripe_user_payment_details_payment_status" AS ENUM('success', 'failed', 'action_required');--> statement-breakpoint
CREATE TYPE "public"."enum_subscription_plans_billing_cycle" AS ENUM('monthly', 'yearly', 'weekly');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_service_order_claim_type" AS ENUM('AUTO', 'WORK');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_service_order_service_type" AS ENUM('SYNC', 'ASYNC');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_service_order_status" AS ENUM('pending', 'accept', 'completed', 'errored', 'cancelled', 'patient_verification_pending', 'archive', 'cancelled_by_provider', 'LabRequested', 'LabReceived', 'schedule_pending', 'lab_approval_pending', 'lab_results_approved', 'lab_results_denied', 'clinical_denial', 'cancelled_by_patient', 'now_show', 'no_show', 'payment_pending', 'pharmacy_pending', 'questionnaire_pending');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_service_order_visit_type" AS ENUM('IN_PERSON', 'ONLINE');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_service_questions_question_for" AS ENUM('male', 'female', 'both');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_service_questions_question_type" AS ENUM('YesNo', 'Text', 'Selection', 'MultipleSelection', 'Date', 'DateTime', 'TextArea', 'Height', 'Weight', 'Bmi', 'FileUpload', 'HeightWeightBmi');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_service_state_mapping_service_type" AS ENUM('SYNC', 'ASYNC');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_services_service_mode" AS ENUM('SYNC', 'ASYNC', 'BOTH_SYNC_ASYNC');--> statement-breakpoint
CREATE TYPE "public"."enum_telehealth_services_service_type" AS ENUM('BUSER', 'AUSER', 'medical_assistant', 'pharmacist', 'DIETICIAN_NUTRITION', 'MENTAL_HEALTH', 'WEIGHT_LOSS_MANAGEMENT', 'DIABETES_PREVENTION');--> statement-breakpoint
CREATE TYPE "public"."enum_tennant_master_preferred_payment_gateway" AS ENUM('PAYPAL', 'STRIPE');--> statement-breakpoint
CREATE TYPE "public"."enum_transactions_payment_method_type" AS ENUM('STRIPE', 'BRAINTREE', 'PAYPAL');--> statement-breakpoint
CREATE TYPE "public"."enum_transactions_payment_status" AS ENUM('pending', 'completed', 'errored', 'cancelled');--> statement-breakpoint
CREATE TYPE "public"."enum_transactions_refund_payment_status" AS ENUM('succeeded', 'failed', 'pending', 'n/a');--> statement-breakpoint
CREATE TYPE "public"."enum_transactions_status" AS ENUM('succeeded', 'failed', 'pending');--> statement-breakpoint
CREATE TYPE "public"."enum_transactions_transaction_status" AS ENUM('initiated', 'completed');--> statement-breakpoint
CREATE TYPE "public"."enum_user_diet_meal_type" AS ENUM('breakfast', 'lunch', 'dinner', 'snacks');--> statement-breakpoint
CREATE TYPE "public"."enum_user_file_repo_details_upload_type" AS ENUM('FAX', 'SFTP');--> statement-breakpoint
CREATE TYPE "public"."enum_user_vitals_mode" AS ENUM('automated', 'doctor', 'patient');--> statement-breakpoint
CREATE TYPE "public"."enum_users_gender" AS ENUM('male', 'female', 'others', 'transgender-female', 'transgender-male');--> statement-breakpoint
CREATE TYPE "public"."enum_users_role" AS ENUM('USER', 'BUSER', 'AUSER', 'medical_assistant', 'viewer', 'support_user', 'pharmacist', 'PHARMACY', 'GROUP_ADMIN', 'SUPPORT_ADMIN', 'DEVELOPER');--> statement-breakpoint
CREATE TYPE "public"."enum_users_status" AS ENUM('AVAILABLE', 'BUSY', 'AWAY', 'OFFLINE', 'ACTIVATION_PENDING', 'ONBOARDING_PENDING', 'PROFILE_INCOMPLETE');--> statement-breakpoint
CREATE TYPE "public"."enum_users_sub_role" AS ENUM('USER', 'DIETICIAN_NUTRITION', 'MENTAL_HEALTH', 'WEIGHT_LOSS_MANAGEMENT', 'DIABETES_PREVENTION');--> statement-breakpoint
CREATE TYPE "public"."enum_visit_summary_upload_status_upload_status" AS ENUM('SUCCESS', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."enum_visit_summary_upload_status_upload_type" AS ENUM('FAX', 'SFTP');--> statement-breakpoint
CREATE TYPE "public"."enum_vitals_summary_upload_status_upload_status" AS ENUM('SUCCESS', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."enum_vitals_summary_upload_status_upload_type" AS ENUM('FAX', 'SFTP');--> statement-breakpoint
CREATE TYPE "public"."enum_webhooks_log_action_type" AS ENUM('webhook', 'prescription_api');--> statement-breakpoint
CREATE TYPE "public"."enum_webhooks_log_status" AS ENUM('pending', 'success', 'failed');--> statement-breakpoint
CREATE TABLE "SequelizeMeta" (
	"name" varchar(255) PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "auth_provider" (
	"auth_id" serial NOT NULL,
	"user_id" integer,
	"json_token_id" text,
	"refresh_token" text,
	"iat" timestamp with time zone,
	"is_jti_valid" boolean DEFAULT false,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "billing" (
	"plan_id" serial NOT NULL,
	"plan_name" text,
	"plan_description" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "contact_us" (
	"contact_us_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"date_requested" timestamp with time zone,
	"title" varchar(1000) DEFAULT '' NOT NULL,
	"question" varchar(1000) DEFAULT '' NOT NULL,
	"queryText" varchar(4000) DEFAULT '' NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "consult_notes" (
	"id" serial PRIMARY KEY NOT NULL,
	"order_id" integer NOT NULL,
	"icd_code" text DEFAULT '',
	"cpt_code" text DEFAULT '',
	"subjective" text DEFAULT '',
	"objective" text DEFAULT '',
	"assessment" text DEFAULT '',
	"plan" text DEFAULT '',
	"erm_id" varchar(150) DEFAULT '',
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"intervention" text,
	"outcome" text,
	"goal" text,
	"order_guid" varchar(50),
	"general_note" varchar(2000) DEFAULT NULL
);
--> statement-breakpoint
CREATE TABLE "affiliate_pharmacy" (
	"id" serial PRIMARY KEY NOT NULL,
	"pharmacy_name" varchar(250) DEFAULT '',
	"street_one" varchar(255) DEFAULT '',
	"street_two" varchar(255) DEFAULT '',
	"city" varchar(75) DEFAULT '',
	"state" varchar(50) DEFAULT '',
	"zip_code" varchar(20) DEFAULT '',
	"country" varchar(50) DEFAULT '',
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone,
	"email" varchar(200),
	"fax_number" varchar(20),
	"phone_number" varchar(20)
);
--> statement-breakpoint
CREATE TABLE "educational_videos" (
	"video_id" serial PRIMARY KEY NOT NULL,
	"session_id" text,
	"archive_id" text,
	"title" text NOT NULL,
	"description" text,
	"url" text,
	"created_by" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"audio_stream_screenshot" text
);
--> statement-breakpoint
CREATE TABLE "encounters_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "requests" (
	"request_id" serial PRIMARY KEY NOT NULL,
	"requestor_id" integer NOT NULL,
	"requestee_id" integer NOT NULL,
	"object_id" integer NOT NULL,
	"status" "enum_requests_status" DEFAULT 'open',
	"message" varchar(255),
	"detail" text,
	"patient_history" text,
	"requestor_read_status" boolean DEFAULT false,
	"requestee_read_status" boolean DEFAULT false,
	"entity_id" integer,
	"add_practice_group_doctors" boolean DEFAULT false,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"order_guid" varchar(50) DEFAULT NULL,
	"release_medical" boolean DEFAULT false,
	"rescheduled" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "encryption_keys" (
	"encryption_key_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"device_id" integer DEFAULT 1 NOT NULL,
	"registration_id" integer NOT NULL,
	"identity_key" text,
	"signed_pre_key" text,
	"pre_key" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "health_summaries_schedule" (
	"schedule_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"start_date" timestamp with time zone,
	"end_date" timestamp with time zone,
	"next_run_date" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "precanned_messages" (
	"message_id" serial PRIMARY KEY NOT NULL,
	"message" text NOT NULL,
	"type" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "feed" (
	"feed_id" serial PRIMARY KEY NOT NULL,
	"description" text,
	"address" text,
	"image_url" text,
	"date" timestamp with time zone,
	"metadata" text,
	"user_id" integer,
	"created_by" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "forms" (
	"form_id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"url" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "promo_codes" (
	"code_id" serial PRIMARY KEY NOT NULL,
	"code_type" "enum_promo_codes_code_type" NOT NULL,
	"service_id" integer,
	"lab_id" integer,
	"discount_type" "enum_promo_codes_discount_type",
	"discount_values" text,
	"promo_code" text,
	"start_date" timestamp with time zone,
	"end_date" timestamp with time zone,
	"usage_type" "enum_promo_codes_usage_type",
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"is_deleted" boolean DEFAULT false,
	"created_by" integer,
	"updated_by" integer,
	"deleted_by" integer
);
--> statement-breakpoint
CREATE TABLE "drugs" (
	"drug_id" serial PRIMARY KEY NOT NULL,
	"category_id" integer,
	"drug_full_name" varchar(200) NOT NULL,
	"tier" varchar(200) NOT NULL,
	"price" varchar(200) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"quantity" varchar(200)
);
--> statement-breakpoint
CREATE TABLE "invitations" (
	"invitation_id" serial PRIMARY KEY NOT NULL,
	"invitor_id" integer NOT NULL,
	"install_type" varchar(255),
	"email" varchar(255),
	"phone" varchar(255),
	"accepted" boolean DEFAULT false,
	"add_practice_group_doctors" boolean DEFAULT false,
	"code" varchar(255),
	"expiry" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "login_requests" (
	"login_request_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"bad_request" boolean DEFAULT false,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "favourite_drugs" (
	"drug_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"drug_name" varchar(255) NOT NULL,
	"brand" varchar(255) NOT NULL,
	"form" varchar(255) NOT NULL,
	"dosage" varchar(255) NOT NULL,
	"quantity" varchar(255) NOT NULL,
	"refill_quantity" varchar(255) NOT NULL,
	"favourite" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"direction_quantity" varchar(255),
	"direction_one" varchar(255),
	"direction_two" varchar(255),
	"tennant_id" varchar(255),
	"pharmacy_name" varchar(255),
	"quantity_unit" varchar(100),
	"erx_product_id" varchar(200),
	"display_order" integer,
	"comments" text
);
--> statement-breakpoint
CREATE TABLE "micromerchant_users" (
	"mm_user_id" serial PRIMARY KEY NOT NULL,
	"first_name" text,
	"last_name" text,
	"zip_code" varchar(255),
	"email" text NOT NULL,
	"phone" varchar(255),
	"gender" varchar(255),
	"data" text,
	"dob" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "procedure_codes" (
	"procedure_code_id" serial PRIMARY KEY NOT NULL,
	"code" varchar(255),
	"description" varchar(500),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean,
	"deleted_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "immunizations_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "medications_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "mms_patients" (
	"patient_id" serial PRIMARY KEY NOT NULL,
	"patient_no" varchar(255) NOT NULL,
	"pharmacy_id" integer,
	"user_id" integer,
	"first_name" varchar(255),
	"last_name" varchar(255),
	"gender" varchar(255),
	"phone" varchar(255),
	"email" varchar(255),
	"active" varchar(255),
	"address" text,
	"notes" text,
	"payment_preference" text,
	"messaging_settings" text,
	"diagnostics" text,
	"allergies" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"insurances" text,
	"source" varchar(255),
	"dob" text,
	"is_smoker" boolean DEFAULT false,
	"marital_status" varchar(20),
	"weight" numeric(10, 3),
	"is_pregnant" boolean DEFAULT false,
	"height" numeric(10, 3),
	"medical_record_number" varchar(50),
	"species_type" varchar(15),
	"dea_restriction_code" varchar(50),
	"family_email" varchar(255),
	"patient_remark" varchar(255),
	"patient_short_remark" varchar(255),
	"family_remark" varchar(255),
	"race" varchar(30),
	"work_phone" varchar(15),
	"mobile" varchar(15),
	"chart_no" varchar(30),
	"language" varchar(30),
	"ez_cap" boolean DEFAULT false,
	"discount_code" varchar(30),
	"short_sode" varchar(30),
	"price_code_brand" varchar(30),
	"price_code_generic" varchar(30),
	"charge_account" varchar(30),
	"print_drug_counselling" varchar(30),
	"category" varchar(30),
	"preferred_delivery_method" varchar(50),
	"driver_license_number" varchar(50),
	"hippa_signature" varchar(50),
	"driver_license_expiry" varchar(50)
);
--> statement-breakpoint
CREATE TABLE "mms_request_payload" (
	"id" integer PRIMARY KEY NOT NULL,
	"rxno" varchar(50) NOT NULL,
	"pharmacy_system" varchar(50) NOT NULL,
	"pharmacy_token" varchar(50),
	"payload" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "mms_prescriptions" (
	"prescription_id" serial PRIMARY KEY NOT NULL,
	"patient_id" integer NOT NULL,
	"rx_no" varchar(255),
	"auth_refills" varchar(255),
	"drug_info" text,
	"qty_ordered" numeric,
	"date_ordered" varchar(255),
	"date_expires" varchar(255),
	"discontinued" varchar(255),
	"sig" varchar(255),
	"prescriber" text,
	"diagnostics" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"auth" varchar(50),
	"source_pms" varchar(50),
	"refill_no" varchar(50),
	"pharmacy_id" integer,
	"date_filled" varchar(50),
	"discontinue_reason" varchar(50),
	"date_discontinued" varchar(50),
	"status" varchar(50),
	"days_supplied" varchar(50),
	"rx_serial_no" varchar(50),
	"bill_type" varchar(50),
	"bill_as" varchar(50),
	"qty_dispensed" varchar(50),
	"delivery_method" varchar(50),
	"hold_rx" varchar(50),
	"rph" varchar(50),
	"date_picked" varchar(30),
	"awp" varchar(50),
	"time_picked" varchar(30),
	"picked_up" varchar(50),
	"prescription_notes" varchar(255),
	"delivery_date" varchar(30),
	"tracking_url" varchar(50),
	"shipping_service" varchar(50),
	"source_script_id" varchar(50),
	"flag340b" varchar(50),
	"daw_code" varchar(50),
	"dispense_as_written" varchar(50),
	"source_script_message" varchar(50),
	"cost_price" varchar(50),
	"rx_amount" varchar(50),
	"disp_fee" varchar(50),
	"dosage_fields" text,
	"patient_copay" varchar(50),
	"billed" varchar(50),
	"primary_insurance" text,
	"fill_list_indicator" varchar(50),
	"submission_clar_code" varchar(50),
	"horizon_graveyard_code" varchar(50),
	"room_number" varchar(10),
	"nursing_home_id" varchar(30),
	"location_code" varchar(20),
	"facility_code" varchar(20),
	"nursing_home" varchar(50),
	"wing_code1" varchar(50),
	"refering_doctor" varchar(50),
	"xfer_to_pharmacy_name" varchar(50),
	"wing_code2" varchar(50),
	"xfer_to_pharmacy_address1" varchar(255),
	"xfer_to_pharmacy_address2" varchar(255),
	"xfer_to_pharmacy_city" varchar(50),
	"xfer_to_pharmacy_phone" varchar(20),
	"xfer_to_pharmacy_npi" varchar(30),
	"xfer_to_pharmacy_ncpdp" varchar(30),
	"bill_status" varchar(50),
	"xfer_to_pharmacy_dea" varchar(30),
	"bill_status_text" varchar(50),
	"workflow_status" varchar(50),
	"prescribed_drug" varchar(50),
	"workflow_status_text" varchar(50),
	"claim_authorization_number" varchar(50),
	"prior_auth_number" varchar(50),
	"election_prescription_origin_time" varchar(50)
);
--> statement-breakpoint
CREATE TABLE "onehealth_lab_orders" (
	"id" serial PRIMARY KEY NOT NULL,
	"order_guid" varchar(50),
	"user_id" integer,
	"registered_kit_id" varchar(50),
	"testing_kit_type" varchar(100) DEFAULT '',
	"total_quantity" integer DEFAULT 1 NOT NULL,
	"use_prescription_service" boolean DEFAULT false,
	"interval" varchar(30),
	"results_needs_revision" boolean DEFAULT false,
	"lab" varchar(30) DEFAULT 'rucdr',
	"additional_data" varchar(30) DEFAULT 'rucdr',
	"posted_data" text,
	"company" varchar(30) DEFAULT 'Ravkoo',
	"return_mailer" varchar(255) DEFAULT '',
	"insurance_enabled" varchar(255) DEFAULT '',
	"kit_ids" varchar(255) DEFAULT '',
	"patient_id" varchar(50) DEFAULT '',
	"hcp_id" varchar(50) DEFAULT '',
	"test_code" varchar(50) DEFAULT '',
	"askOnEntry" varchar(50) DEFAULT '',
	"diagnostic_code" varchar(255) DEFAULT '',
	"diagnose_observation_date" timestamp with time zone,
	"lab_ref_id" varchar(255) DEFAULT '',
	"status" "enum_onehealth_lab_orders_status" DEFAULT 'PENDING',
	"payment_status" "enum_onehealth_lab_orders_payment_status" DEFAULT 'PENDING',
	"one_health" text,
	"collection_at" timestamp with time zone,
	"kit_register_at" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"service_charges" numeric DEFAULT '0',
	"shipping_amount" numeric DEFAULT '0',
	"total_cost" numeric DEFAULT '0',
	"lab_id" varchar(30) DEFAULT NULL,
	"payment_transaction" text,
	"flat_price" numeric DEFAULT '0',
	"name" varchar(150) DEFAULT '',
	"product_code" varchar(100) DEFAULT '',
	"discount" numeric DEFAULT '0',
	CONSTRAINT "onehealth_lab_orders_order_guid_key" UNIQUE("order_guid")
);
--> statement-breakpoint
CREATE TABLE "patient_insurances" (
	"patient_insurance_id" serial PRIMARY KEY NOT NULL,
	"patient_id" integer NOT NULL,
	"insurance_id" varchar(255),
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "permission_groups" (
	"patient_id" integer NOT NULL,
	"associated_user_id" integer NOT NULL,
	"group_id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "permission_groups_patient_id_associated_user_id_key" UNIQUE("patient_id","associated_user_id")
);
--> statement-breakpoint
CREATE TABLE "telehealth_service_order" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_id" integer,
	"answer_given_by" integer,
	"provider_id" integer,
	"order_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"order_guid" varchar(50),
	"status" "enum_telehealth_service_order_status",
	"prescription_delivery" boolean DEFAULT false,
	"ravkoo_prescription_option" boolean DEFAULT false,
	"pharmacy_name" varchar(100) DEFAULT '',
	"pharmacy_phone" varchar(20) DEFAULT '',
	"pharmacy_address" varchar(255) DEFAULT '',
	"service_type" "enum_telehealth_service_order_service_type" DEFAULT 'SYNC',
	"release_medical" boolean DEFAULT false,
	"release_medical_at" timestamp with time zone,
	"is_cancelled_by_provider" boolean DEFAULT false,
	"cancelled_at" timestamp with time zone,
	"pharmacy_city" varchar(20),
	"pharmacy_state" varchar(20),
	"pharmacy_zip" varchar(20),
	"pharmacy_fax" varchar(20),
	"pharmacy_preference" integer DEFAULT 0 NOT NULL,
	"claim_type" "enum_telehealth_service_order_claim_type",
	"claim_id" varchar(255),
	"payor_name" varchar(255),
	"injury_date" timestamp with time zone,
	"current_medicines" varchar(255),
	"allergies_to_medicines" varchar(255),
	"other_allergies" varchar(255),
	"doctor_notes" varchar(255),
	"abnormal_findings" varchar(255),
	"terms_and_conditions_accepted" boolean DEFAULT false,
	"cancellation_reason" varchar(255),
	"is_refill_request" boolean DEFAULT false,
	"external_order_id" varchar(50),
	"follow_up" integer,
	"visit_type" "enum_telehealth_service_order_visit_type",
	"completion_reason" varchar(255),
	"session_type" varchar(255),
	"schedule_type" varchar(255),
	"affiliateid" varchar(255),
	"client_name" varchar(255),
	"dosespot_pharmacy_id" varchar(200),
	"pharmacy_ncpdp_id" varchar(255),
	"erx_prescription_visitied_at" timestamp with time zone,
	"completed_at" timestamp with time zone,
	"pharmacy_id" integer,
	CONSTRAINT "telehealth_service_order_order_guid_key" UNIQUE("order_guid")
);
--> statement-breakpoint
CREATE TABLE "orders" (
	"id" serial PRIMARY KEY NOT NULL,
	"order_id" text NOT NULL,
	"schedule_id" integer,
	"start_time" timestamp with time zone,
	"end_time" timestamp with time zone,
	"caller_id" integer,
	"callee_id" integer,
	"doctor_id" integer,
	"status" "enum_orders_status",
	"type" "enum_orders_type",
	"category" "enum_orders_category",
	"conversation_mode" text,
	"cost" numeric,
	"billed" boolean DEFAULT false,
	"caller_location" varchar(255),
	"callee_location" varchar(255),
	"instructions" text,
	"diagnosis" text,
	"procedure" text,
	"visit_summary" text,
	"regenerate_visit_summary" boolean DEFAULT false,
	"regenerate_ccd_file" boolean DEFAULT false,
	"is_virtual_room" boolean DEFAULT false,
	"hide_visit_details" boolean DEFAULT false,
	"audio_stream_screenshot" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"currency" text,
	"order_guid" varchar(50) DEFAULT NULL,
	"duration" varchar(50),
	"host_pass_phrase" varchar(255),
	"viewer_pass_phrase" varchar(255),
	"channel" varchar(255),
	CONSTRAINT "orders_order_id_key" UNIQUE("order_id")
);
--> statement-breakpoint
CREATE TABLE "practice_groups" (
	"practice_group_id" serial PRIMARY KEY NOT NULL,
	"name" text,
	"phones" text,
	"visit_address" text,
	"install_type" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "mobile_number_otp_validator" (
	"id" uuid PRIMARY KEY NOT NULL,
	"phone" varchar(20) NOT NULL,
	"otp" varchar(1000) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "consult_update_details_history" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_order_id" integer,
	"user_id" integer NOT NULL,
	"previous_values" varchar(2000),
	"updated_values" varchar(2000),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "provider_license" (
	"provider_license_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"license_number" varchar(255),
	"license_state" integer,
	"license_state_name" varchar(255),
	"license_expiration_date" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "requests_log" (
	"request_log_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"install_type" varchar(255),
	"method" varchar(255) NOT NULL,
	"response_http_status" integer NOT NULL,
	"endpoint" text NOT NULL,
	"timestamp" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "request_objects" (
	"object_id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"type" varchar(255) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "medicines" (
	"medicine_id" serial PRIMARY KEY NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" text,
	"brand" varchar(500),
	"form" varchar(500),
	"dosage" varchar(500),
	"quantity" varchar(500),
	"refill_quantity" varchar(500),
	"direction_quantity" varchar(500),
	"direction_one" varchar(500),
	"direction_two" varchar(500),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "medicine_service_pharmacy_mapping" (
	"id" serial PRIMARY KEY NOT NULL,
	"medicine_id" integer NOT NULL,
	"pharmacy_id" integer NOT NULL,
	"service_id" integer NOT NULL,
	"status" boolean DEFAULT true
);
--> statement-breakpoint
CREATE TABLE "chat_rooms" (
	"id" serial PRIMARY KEY NOT NULL,
	"room_name" text NOT NULL,
	"room_identifier" uuid DEFAULT gen_random_uuid(),
	"deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"service_key" text,
	"description" text,
	"last_message_at" timestamp,
	"last_message" text
);
--> statement-breakpoint
CREATE TABLE "chat_room_members" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" serial NOT NULL,
	"room_id" serial NOT NULL,
	"deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "transactions" (
	"transaction_id" text PRIMARY KEY NOT NULL,
	"payer_user_id" integer NOT NULL,
	"payee_user_id" integer,
	"amount" double precision NOT NULL,
	"currency" text NOT NULL,
	"status" "enum_transactions_status" NOT NULL,
	"description" text,
	"error_description" text,
	"card_details" text,
	"line_items" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"global_id" varchar(100),
	"refund_transaction_id" varchar(100),
	"refund_created_at" timestamp with time zone,
	"refund_global_id" varchar(100),
	"order_guid" varchar(50) DEFAULT NULL,
	"payment_method_type" "enum_transactions_payment_method_type" DEFAULT 'BRAINTREE',
	"refund_payment_status" "enum_transactions_refund_payment_status" DEFAULT 'n/a',
	"refund_payment_amount" double precision DEFAULT 0,
	"refund_error_description" text,
	"refund_success_response" text,
	"transaction_status" varchar(100)
);
--> statement-breakpoint
CREATE TABLE "special_discounts" (
	"discount_id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"discount_price" integer NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "stripe_user_payment_details" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"payment_method_id" text,
	"off_session_payment_allowed" boolean,
	"payment_status" "enum_stripe_user_payment_details_payment_status" DEFAULT 'success' NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "telehealth_service_procedure_codes_mapping" (
	"telehealth_service_procedure_codes_mapping_id" serial PRIMARY KEY NOT NULL,
	"service_id" integer NOT NULL,
	"procedure_code_id" integer NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean,
	"deleted_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "telehealth_service_questions" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_id" integer,
	"question" varchar(500) NOT NULL,
	"help_text" varchar(200),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"selection_option" text,
	"question_type" "enum_telehealth_service_questions_question_type" DEFAULT 'YesNo',
	"display_order" integer DEFAULT 1,
	"parent_id" integer,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"halt_on_selection_option" varchar(500),
	"is_pre_questions" boolean DEFAULT false,
	"is_optional" boolean DEFAULT false,
	"question_for" "enum_telehealth_service_questions_question_for" DEFAULT 'both'
);
--> statement-breakpoint
CREATE TABLE "telehealth_service_provider_mapping" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_id" integer,
	"provider_id" integer,
	"cost_price" numeric DEFAULT '0',
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"status" boolean DEFAULT true
);
--> statement-breakpoint
CREATE TABLE "subscription_plans" (
	"plan_id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"price" integer NOT NULL,
	"currency" text NOT NULL,
	"billing_cycle" "enum_subscription_plans_billing_cycle" NOT NULL,
	"billing_interval" integer NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"paypal_plan_id" text,
	"service_id" integer,
	"service_key" varchar(255),
	"service_master_id" integer,
	"recurly_plan_id" varchar(255),
	"stripe_plan_id" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "stripe_user_details" (
	"user_id" integer PRIMARY KEY NOT NULL,
	"stripe_user_id" text NOT NULL,
	"cc_status" "enum_stripe_user_details_cc_status" DEFAULT 'not_captured',
	"oauth_status" "enum_stripe_user_details_oauth_status" DEFAULT 'not_connected',
	"stripe_account_id" text,
	"oauth_verification_token" text,
	"currency_code" text,
	"default_payment_method" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "social_history_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_particlehealth" (
	"health_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"query_id" varchar(255),
	"status" varchar(255),
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"buser_id" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_details" (
	"user_detail_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"data" text,
	"signature" varchar(255),
	"specialty" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"address" text,
	"deleted" boolean DEFAULT false,
	"bio" text,
	"language" json DEFAULT '[]'::json
);
--> statement-breakpoint
CREATE TABLE "user_educational_videos" (
	"user_video_id" serial PRIMARY KEY NOT NULL,
	"video_id" serial NOT NULL,
	"referred_by" integer NOT NULL,
	"referred_for" integer NOT NULL,
	"doctor_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"viewed_at" timestamp with time zone,
	"viewed" boolean
);
--> statement-breakpoint
CREATE TABLE "products" (
	"product_id" serial PRIMARY KEY NOT NULL,
	"product_name" varchar(200) NOT NULL,
	"description" varchar(2000) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "user_identities" (
	"user_identity_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"identifier" text,
	"type" varchar(255),
	"install_type" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "follow_up_reminder" (
	"id" serial PRIMARY KEY NOT NULL,
	"order_id" integer,
	"user_id" integer,
	"last_follow_up_sent" varchar(50),
	"error_message" varchar(200),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"next_follow_up" timestamp with time zone,
	"status" "enum_follow_up_reminder_status" DEFAULT 'pending',
	"tennant_id" integer,
	"next_order_id" integer,
	"follow_up_sent_date" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_file_repo_details" (
	"repo_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"upload_type" "enum_user_file_repo_details_upload_type" NOT NULL,
	"connection_details" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_health_summary" (
	"summary_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"type" varchar(255),
	"data" text,
	"source_platform" varchar(255),
	"has_detail" boolean DEFAULT false,
	"date" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_subscription_billing" (
	"billing_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"plan_id" integer NOT NULL,
	"base_plan_price" integer NOT NULL,
	"discount" integer NOT NULL,
	"invoice_number" text,
	"special_discounts" text,
	"addons" text,
	"date" timestamp with time zone,
	"total_price" text DEFAULT 0,
	"billed" boolean DEFAULT false,
	"invoice_pdf_path" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "drug_days" (
	"id" serial PRIMARY KEY NOT NULL,
	"drug_id" integer,
	"drug_days" integer NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "consult_order_files" (
	"file_id" serial PRIMARY KEY NOT NULL,
	"order_id" integer,
	"type" "enum_consult_order_files_type",
	"file_path" text NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"tennant_id" integer
);
--> statement-breakpoint
CREATE TABLE "user_subscription" (
	"user_subscription_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"plan_id" integer NOT NULL,
	"start_date" timestamp with time zone NOT NULL,
	"no_of_intervals" integer,
	"referral_code" text,
	"discount" integer DEFAULT 0,
	"next_billing_date" timestamp with time zone,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"subscription_id" text,
	"status" text,
	"service_id" integer,
	"service_key" varchar(255),
	"service_master_id" integer
);
--> statement-breakpoint
CREATE TABLE "sms_template" (
	"sms_template_id" serial PRIMARY KEY NOT NULL,
	"sms_action" varchar(100) NOT NULL,
	"sms_template_name" varchar(500) NOT NULL,
	"service_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "user_vitals_documents" (
	"user_vitals_document_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"doctor_id" integer,
	"path" text,
	"start_time" timestamp with time zone,
	"end_time" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "chat_messages" (
	"id" serial PRIMARY KEY NOT NULL,
	"message" text,
	"sender_id" serial NOT NULL,
	"room_id" serial NOT NULL,
	"deleted" boolean DEFAULT false NOT NULL,
	"deleted_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"file_id" integer,
	"read_by" text[] DEFAULT '{"RAY"}' NOT NULL
);
--> statement-breakpoint
CREATE TABLE "jobs" (
	"id" serial PRIMARY KEY NOT NULL,
	"path" text,
	"order_id" integer NOT NULL,
	"referral_id" integer NOT NULL,
	"status" "enum_jobs_status" DEFAULT 'pending',
	"failed_message" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "user_schedules" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"is_available" boolean DEFAULT true,
	"end_datetime" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"created_by" integer,
	"updated_by" integer,
	"deleted_by" integer,
	"schedule_date" varchar(30) DEFAULT '' NOT NULL,
	"start_datetime" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "chat_files" (
	"file_id" serial PRIMARY KEY NOT NULL,
	"user_id" serial NOT NULL,
	"room_id" serial NOT NULL,
	"name" text,
	"path" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "email_template" (
	"email_template_id" serial PRIMARY KEY NOT NULL,
	"email_action" varchar(100) NOT NULL,
	"email_template_name" varchar(500) NOT NULL,
	"service_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "prescription_preference" (
	"preference_id" serial PRIMARY KEY NOT NULL,
	"pharmacy_id" integer,
	"tennant_id" integer,
	"pharmacy_name" varchar(100),
	"client_name" varchar(100),
	"preference" "enum_prescription_preference_preference" DEFAULT 'fax',
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "lifefile_configuration" (
	"lifefile_config_id" serial PRIMARY KEY NOT NULL,
	"tennant_id" integer,
	"pharmacy_id" integer,
	"pharmacy_name" varchar(100),
	"client_name" varchar(100),
	"lifefile_url" varchar(200) NOT NULL,
	"api_username" varchar(100) NOT NULL,
	"api_password" varchar(100) NOT NULL,
	"practice_id" integer NOT NULL,
	"practice_name" varchar(100) NOT NULL,
	"vendor_id" integer NOT NULL,
	"location_id" integer NOT NULL,
	"network_id" integer NOT NULL,
	"network_name" varchar(100) NOT NULL,
	"shipping_services" "enum_lifefile_configuration_shipping_services" DEFAULT '7780',
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "tennant_master" (
	"id" serial PRIMARY KEY NOT NULL,
	"tennant_name" varchar(255) NOT NULL,
	"pharmacy_fax" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"filter_display" boolean DEFAULT false,
	"parent_id" integer,
	"lab_fax_number" varchar(255),
	"support_email" varchar(255),
	"logo_url" varchar(255),
	"paypal_client_id" text,
	"paypal_client_secret" text,
	"access_lab_client_number" varchar(2000),
	"twilio_account_sid" text,
	"twilio_phone_number" text,
	"twilio_auth_token" text,
	"stripe_client_id" varchar(2000),
	"stripe_client_secret" varchar(2000),
	"preferred_payment_gateway" "enum_tennant_master_preferred_payment_gateway",
	"mailchip_from_email" varchar(255),
	"mailchip_from_email_name" varchar(255),
	"tennant_display_name" varchar(255),
	"tenant_access" varchar(500),
	"dosespot_client_id" varchar(1000),
	"dosespot_client_secret" varchar(1000),
	"configured_domain" varchar(200),
	"tennant_guid" uuid DEFAULT uuid_generate_v4() NOT NULL,
	"default_provider_id" integer,
	"show_powered_by" boolean DEFAULT true,
	"favicon_url" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "consult_reassign_history" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_order_id" integer NOT NULL,
	"previous_provider" integer NOT NULL,
	"updated_provider" integer NOT NULL,
	"reassigned_by" integer NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "telehealth_service_question_answer_dump" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_order_id" integer,
	"question_text" varchar(2000) NOT NULL,
	"answer" varchar(2000) DEFAULT false,
	"other_text" varchar(2000),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"selection_option" text
);
--> statement-breakpoint
CREATE TABLE "external_requests_log" (
	"external_request_log_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"type" varchar(255) NOT NULL,
	"detail" text NOT NULL,
	"timestamp" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "mms_patient_invitations" (
	"invitation_id" serial PRIMARY KEY NOT NULL,
	"email" text,
	"phone" text,
	"code" text NOT NULL,
	"mms_patient_id" integer NOT NULL,
	"accepted" boolean DEFAULT false,
	"expiry" timestamp with time zone,
	"remaining_tries" integer DEFAULT 3,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "pharmacies" (
	"pharmacy_id" serial PRIMARY KEY NOT NULL,
	"npi" varchar(255),
	"name" varchar(255),
	"nabp" varchar(255),
	"address" text,
	"phone" varchar(255),
	"fax" varchar(255),
	"email" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"ncpdpid" varchar(50),
	"pharmcist_name" varchar(50),
	"dea" varchar(50),
	"pharmacy_legal_name" varchar(100),
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false NOT NULL,
	"pharmacies_guid" uuid DEFAULT uuid_generate_v4() NOT NULL,
	CONSTRAINT "pharmacies_npi_key" UNIQUE("npi")
);
--> statement-breakpoint
CREATE TABLE "service_payment_mapping" (
	"service_payment_mapping_id" serial PRIMARY KEY NOT NULL,
	"service_key" varchar(255),
	"service_master_id" integer,
	"payment_details_id" integer NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone,
	"payment_type" "enum_service_payment_mapping_payment_type" DEFAULT 'subscription'
);
--> statement-breakpoint
CREATE TABLE "users" (
	"user_id" serial PRIMARY KEY NOT NULL,
	"user_guid" varchar(255),
	"password" text,
	"role" "enum_users_role" DEFAULT 'USER',
	"status" "enum_users_status" DEFAULT 'OFFLINE',
	"install_type" varchar(255),
	"first_name" text,
	"last_name" text,
	"zip_code" varchar(255),
	"email" text NOT NULL,
	"phone" text,
	"otp" text,
	"dob" text,
	"appt_length" integer,
	"appt_start_time" varchar(255),
	"appt_end_time" varchar(255),
	"secure_message" boolean DEFAULT false,
	"connection_requests" boolean DEFAULT false,
	"vitals_ccd_enabled" boolean DEFAULT false,
	"appt_requests" boolean DEFAULT false,
	"trial_validity" timestamp with time zone,
	"is_cc_captured" boolean,
	"gender" "enum_users_gender" DEFAULT 'male',
	"deleted" boolean DEFAULT false,
	"email_verified" boolean DEFAULT false,
	"user_avatar" text,
	"id_card" text,
	"history" text,
	"questionnaire" text,
	"token_validity" integer,
	"email_verification_details" text,
	"last_active" timestamp with time zone,
	"locked" timestamp with time zone,
	"registration_key" text,
	"is_rpm_enabled" boolean DEFAULT false,
	"is_notify_on_capture" boolean DEFAULT false,
	"vitals_thresholds" text,
	"vitals_cron" text,
	"app_details" text,
	"history_updated_at" timestamp with time zone,
	"questionnaire_updated_at" timestamp with time zone,
	"debug" boolean DEFAULT false,
	"invitation_code_validity" integer,
	"cron_expression" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"mirth_ccd_enabled" boolean DEFAULT false,
	"cc_payment_accepted" boolean DEFAULT false,
	"recording_enabled" boolean DEFAULT true,
	"transcription_enabled" boolean DEFAULT true,
	"orders_enabled" boolean DEFAULT true,
	"show_health_summaries" boolean,
	"healthgorilla_id" varchar(255),
	"release_medical" boolean,
	"telehealth_service_cost" numeric DEFAULT '0',
	"sub_role" "enum_users_sub_role" DEFAULT 'USER',
	"my_invite_code" varchar(15) DEFAULT NULL,
	"referred_by_invite_code" varchar(15) DEFAULT NULL,
	"referred_by_user_id" integer,
	"email_2" text,
	"tennant_id" integer,
	"secondary_phone" text,
	"is_tennant_owner" boolean DEFAULT false NOT NULL,
	"send_email_campaign" boolean DEFAULT false NOT NULL,
	"app_timezone" varchar(255) DEFAULT '{}',
	"rxPersonId" varchar(50),
	"rxStatus" boolean DEFAULT false,
	"tenant_access" varchar(500),
	"dosespot_api_response" varchar(2000),
	"id_card_file" text,
	"pharmacy_access" varchar(500),
	"parent_id" integer,
	"dependent_account_relation" varchar(255),
	"tracking_code" varchar(255),
	CONSTRAINT "users_my_invite_code_key" UNIQUE("my_invite_code")
);
--> statement-breakpoint
CREATE TABLE "referral_tracking" (
	"id" serial PRIMARY KEY NOT NULL,
	"referral_id" integer,
	"order_guid" varchar(50),
	"shipping_partner" "enum_referral_tracking_shipping_partner",
	"tracking_number" varchar(500),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "support_notes" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"support_user_id" integer NOT NULL,
	"order_guid" varchar(50),
	"general_note" text DEFAULT '',
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "tenant_auth_provider" (
	"auth_id" serial PRIMARY KEY NOT NULL,
	"tenant_id" integer NOT NULL,
	"secret_token" varchar(255) NOT NULL,
	"auth_token" varchar(255) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "tennant_config" (
	"id" serial PRIMARY KEY NOT NULL,
	"tennant_id" integer,
	"theme_color" json NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL
);
--> statement-breakpoint
CREATE TABLE "service_action_preference" (
	"service_action_preference_id" serial PRIMARY KEY NOT NULL,
	"service_id" integer NOT NULL,
	"tenant_id" integer NOT NULL,
	"action_type" varchar(255) NOT NULL,
	"webhook_url" varchar(255),
	"sms_template" varchar(255),
	"email_template" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone,
	"external_api_integration" varchar(255),
	"api_key" text,
	"api_secret" text
);
--> statement-breakpoint
CREATE TABLE "telehealth_service_master" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(200) NOT NULL,
	"description" varchar(500),
	"service_master_guid" uuid DEFAULT uuid_generate_v4() NOT NULL,
	"tennant_id" integer,
	"created_at" timestamp with time zone NOT NULL,
	"updated_at" timestamp with time zone NOT NULL,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"service_key" varchar(500),
	"initial_service_id" integer
);
--> statement-breakpoint
CREATE TABLE "telehealth_services" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_name" varchar(200) NOT NULL,
	"description" varchar(2000) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"service_type" "enum_telehealth_services_service_type" DEFAULT 'BUSER',
	"display_order" integer DEFAULT 1,
	"service_mode" "enum_telehealth_services_service_mode" DEFAULT 'BOTH_SYNC_ASYNC',
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone,
	"amount" integer,
	"tenant_id" integer,
	"paypal_plan_id" text,
	"title" varchar(255),
	"subtitle" varchar(2000),
	"service_key" varchar(255),
	"session_type" varchar(255),
	"fields_options" varchar(255),
	"on_complete_script" varchar(2000),
	"on_follow_up_script" varchar(2000),
	"access_labs_test_code" varchar(2000),
	"on_create_script" varchar(2000),
	"on_lab_result_received_script" varchar(2000),
	"on_update_schedule_script" varchar(2000),
	"disclaimer" text,
	"service_details" text,
	"is_video_call" boolean DEFAULT true NOT NULL,
	"is_audio_call" boolean DEFAULT true NOT NULL,
	"display_questionnaire" boolean DEFAULT false NOT NULL,
	"display_service_name" text,
	"erx_drug_key" varchar(255),
	"filter_display" boolean DEFAULT false NOT NULL,
	"services_guid" uuid DEFAULT uuid_generate_v4() NOT NULL,
	"original_amount" varchar(255),
	"duration_text" varchar(255),
	"discount_text" varchar(255),
	"consult_instruction" json,
	"service_instruction" json,
	"service_master_id" integer,
	"next_service_id" integer,
	"eligible_messages" json,
	"display_amount" varchar(255) DEFAULT NULL,
	"user_consent" varchar(255),
	CONSTRAINT "telehealth_services_service_name_key" UNIQUE("service_name")
);
--> statement-breakpoint
CREATE TABLE "diagnoses_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "family_history_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "procedures_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "care_plan_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "results_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "allergies_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "document_values" (
	"value_id" serial PRIMARY KEY NOT NULL,
	"summary_id" integer NOT NULL,
	"key" varchar(255),
	"value" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "mms_prescription_refills" (
	"refill_id" serial PRIMARY KEY NOT NULL,
	"prescription_id" integer NOT NULL,
	"refill_no" varchar(255),
	"date_filled" varchar(255),
	"date_picked" varchar(255),
	"qty_ordered" numeric,
	"qty_dispensed" numeric,
	"notes" text,
	"delivery_method" text,
	"data" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"cost_price" numeric,
	"disp_fee" numeric,
	"rx_amount" numeric,
	"patient_copay" numeric,
	"billed_amount" numeric,
	"primary_insurance_amount" numeric,
	"primary_insurance_code" varchar(255),
	"secondary_insurance_amount" numeric,
	"tertiary_insurance_amount" numeric,
	"status" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "vitals_summary_upload_status" (
	"status_id" serial PRIMARY KEY NOT NULL,
	"doctor_id" integer NOT NULL,
	"patient_id" integer NOT NULL,
	"upload_type" "enum_vitals_summary_upload_status_upload_type" NOT NULL,
	"upload_status" "enum_vitals_summary_upload_status_upload_status" DEFAULT 'SUCCESS' NOT NULL,
	"doc_path" text,
	"start_date" timestamp with time zone,
	"end_date" timestamp with time zone,
	"fax_id" integer,
	"fax_status" text,
	"error" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vitals_notification_cycle" (
	"user_id" integer PRIMARY KEY NOT NULL,
	"start_date" timestamp with time zone,
	"end_date" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "visit_summary_upload_status" (
	"status_id" serial PRIMARY KEY NOT NULL,
	"order_id" text NOT NULL,
	"user_id" integer NOT NULL,
	"upload_type" "enum_visit_summary_upload_status_upload_type" NOT NULL,
	"upload_status" "enum_visit_summary_upload_status_upload_status" DEFAULT 'SUCCESS' NOT NULL,
	"error" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vitals_patient_monitoring" (
	"patient_id" integer NOT NULL,
	"buser_id" integer NOT NULL,
	"group_id" serial PRIMARY KEY NOT NULL,
	"start_date" timestamp with time zone,
	"end_date" timestamp with time zone,
	"billed" boolean DEFAULT false,
	"billed_date" timestamp with time zone,
	"cost" numeric,
	"currency" text,
	"procedure" text DEFAULT '{"CPT_codes":[{"code":"99454","description":"Device(s) supply with daily recording(s) or programmed alert(s) transmission, each 30 days"}]}',
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_forms" (
	"user_form_id" serial PRIMARY KEY NOT NULL,
	"form_id" integer,
	"assigned_by" integer,
	"assigned_to" integer,
	"doctor_id" integer,
	"filled_form_url" text,
	"data" text,
	"status" varchar(255),
	"score" numeric,
	"order_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_files" (
	"user_file_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"created_by" integer,
	"doctor_id" integer,
	"name" varchar(255),
	"path" varchar(255),
	"order_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_diet" (
	"user_diet_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"meal_type" "enum_user_diet_meal_type" NOT NULL,
	"date" timestamp with time zone,
	"image_url" text,
	"serving" integer,
	"serving_unit" varchar(255),
	"food" text,
	"order_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "telehealth_service_question_answer" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_order_id" integer,
	"question_id" integer,
	"answer" varchar(2000),
	"other_text" varchar(2000),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "faxes" (
	"fax_id" serial PRIMARY KEY NOT NULL,
	"fax_sid" text NOT NULL,
	"fax_number" text NOT NULL,
	"status" text NOT NULL,
	"referral_id" integer,
	"detail" text,
	"media_url" text,
	"sent_by" integer,
	"sent_for" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "reviews" (
	"review_id" serial PRIMARY KEY NOT NULL,
	"order_id" text,
	"review" text NOT NULL,
	"given_by" integer,
	"given_to" integer,
	"rating" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"call_quality" integer DEFAULT 0,
	"is_recommend_provider" boolean,
	"consultation_rating" integer DEFAULT 0,
	"user_comment" text DEFAULT ''
);
--> statement-breakpoint
CREATE TABLE "referrals" (
	"referral_id" serial PRIMARY KEY NOT NULL,
	"referred_by" integer,
	"referred_for" integer,
	"doctor_id" integer,
	"type" text NOT NULL,
	"name" text,
	"path" text NOT NULL,
	"status" "enum_referrals_status",
	"order_id" integer,
	"detail" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"product_id" integer,
	"prescription_viewed" boolean DEFAULT false,
	"prescription_processed_by" varchar(100),
	"lifefile_order_id" integer,
	"dosespot_prescription_id" integer
);
--> statement-breakpoint
CREATE TABLE "transcriptions" (
	"transcription_id" serial PRIMARY KEY NOT NULL,
	"order_id" text,
	"tuser_id" integer,
	"status" text,
	"transcript" text,
	"updated_transcript" text,
	"audio_stream" text,
	"archive_id" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_vitals" (
	"user_vital_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"value" text,
	"metric" varchar(255),
	"detail" text,
	"type" varchar(255),
	"display_name" varchar(255),
	"mode" "enum_user_vitals_mode" DEFAULT 'automated',
	"billed" boolean DEFAULT false,
	"procedure" text,
	"abnormal" boolean DEFAULT false,
	"entity_id" integer,
	"bundle_id" varchar(255),
	"date" timestamp with time zone,
	"description" text,
	"order_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"currency" text,
	"cost" numeric,
	"source_platform" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "provider_ratings" (
	"id" serial PRIMARY KEY NOT NULL,
	"order_id" integer,
	"provider_id" integer,
	"user_id" integer,
	"rating" integer DEFAULT 1,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "schedules" (
	"schedule_id" serial PRIMARY KEY NOT NULL,
	"scheduled_with" integer,
	"scheduled_by" integer,
	"start_year" integer NOT NULL,
	"start_month" integer NOT NULL,
	"start_day" integer NOT NULL,
	"end_year" integer NOT NULL,
	"end_month" integer NOT NULL,
	"end_day" integer NOT NULL,
	"start" timestamp with time zone NOT NULL,
	"end" timestamp with time zone NOT NULL,
	"detail" text,
	"patient_history" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"order_guid" varchar(50) DEFAULT NULL,
	"release_medical" boolean DEFAULT false,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"order_id" integer
);
--> statement-breakpoint
CREATE TABLE "tokbox_archive_type" (
	"session_id" text PRIMARY KEY NOT NULL,
	"archive_id" text,
	"type" text NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "health_summary_metadata" (
	"metadata_id" serial PRIMARY KEY NOT NULL,
	"category" text NOT NULL,
	"response_type" text,
	"template" text,
	"array_fields" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "health_summaries_log" (
	"log_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"success" boolean DEFAULT false,
	"summary_details" text,
	"step" varchar(255),
	"message" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "conversation_messages" (
	"cm_id" serial PRIMARY KEY NOT NULL,
	"message" text,
	"user_id" integer,
	"c_id" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "conversations" (
	"c_id" serial PRIMARY KEY NOT NULL,
	"user_one" integer,
	"user_two" integer,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "prescription_transfer_medications" (
	"medication_id" serial PRIMARY KEY NOT NULL,
	"request_id" integer,
	"name" text NOT NULL,
	"is_fulfilled" boolean DEFAULT false,
	"hg_rx_id" integer,
	"status" "enum_prescription_transfer_medications_status" DEFAULT 'pending',
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "prescription_transfer_request" (
	"request_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer,
	"zipcode" text NOT NULL,
	"pharmacy_name" text NOT NULL,
	"pharmacy_address" text NOT NULL,
	"first_name" text NOT NULL,
	"last_name" text NOT NULL,
	"transfer_all" boolean DEFAULT false NOT NULL,
	"phone" text NOT NULL,
	"dob" text NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "refill_request" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_order_id" integer NOT NULL,
	"drug_details" varchar(5000) NOT NULL,
	"prescription_images" varchar(5000) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "telehealth_service_state_mapping" (
	"id" serial PRIMARY KEY NOT NULL,
	"state_id" integer NOT NULL,
	"service_id" integer,
	"status" boolean DEFAULT true,
	"service_type" "enum_telehealth_service_state_mapping_service_type"
);
--> statement-breakpoint
CREATE TABLE "pharmacy_state_service_mapping" (
	"id" serial PRIMARY KEY NOT NULL,
	"service_id" integer,
	"state_id" integer,
	"pharmacy_id" integer,
	"status" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE "payment_details" (
	"payment_details_id" serial PRIMARY KEY NOT NULL,
	"payment_gateway" "enum_payment_details_payment_gateway" NOT NULL,
	"payment_gateway_code" varchar(255),
	"payment_gateway_details" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone,
	"tennant_id" integer
);
--> statement-breakpoint
CREATE TABLE "states" (
	"state_id" serial PRIMARY KEY NOT NULL,
	"name" varchar(50) NOT NULL,
	"abbreviation" varchar(2) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone,
	"is_async" boolean DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE TABLE "drugs_category" (
	"category_id" serial PRIMARY KEY NOT NULL,
	"category_name" varchar(200) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted_at" timestamp with time zone,
	"deleted" boolean DEFAULT false
);
--> statement-breakpoint
CREATE TABLE "webhooks_log" (
	"webhooks_log_id" serial PRIMARY KEY NOT NULL,
	"body" varchar(5000),
	"url" varchar(255) NOT NULL,
	"response_status" varchar(255),
	"case_id" varchar(255) NOT NULL,
	"status" "enum_webhooks_log_status",
	"failed_message" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone,
	"type" varchar(255),
	"headers" text,
	"action_type" "enum_webhooks_log_action_type"
);
--> statement-breakpoint
CREATE TABLE "user_insurance" (
	"user_insurance_id" serial PRIMARY KEY NOT NULL,
	"user_id" integer NOT NULL,
	"insurance_member_id" varchar(255) NOT NULL,
	"insurance_plan_name" varchar(255) NOT NULL,
	"payer_identification" varchar(255) NOT NULL,
	"cover_type" varchar(255) NOT NULL,
	"user_insurance_id_front" varchar(255),
	"user_insurance_id_back" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean,
	"deleted_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "insurance_eligibility_logs" (
	"insurance_eligibility_log_id" serial PRIMARY KEY NOT NULL,
	"order_id" integer NOT NULL,
	"insurance_member_id" varchar(255) NOT NULL,
	"full_response" text NOT NULL,
	"is_eligible" boolean NOT NULL,
	"ineligible_reason" text,
	"benefit_type" varchar(255),
	"benefit_amount" varchar(255),
	"benefit_percentage" varchar(255),
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"deleted" boolean DEFAULT false,
	"deleted_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "user_practice_groups" (
	"user_id" integer NOT NULL,
	"practice_group_id" integer NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "user_practice_groups_pkey" PRIMARY KEY("user_id","practice_group_id")
);
--> statement-breakpoint
CREATE TABLE "schedule_translation" (
	"schedule_id" integer NOT NULL,
	"language_code" varchar(255) NOT NULL,
	"patient_history" text,
	"detail" text,
	CONSTRAINT "schedule_translation_pkey" PRIMARY KEY("schedule_id","language_code")
);
--> statement-breakpoint
CREATE TABLE "request_translation" (
	"request_id" integer NOT NULL,
	"language_code" varchar(255) NOT NULL,
	"message" text,
	"patient_history" text,
	"detail" text,
	CONSTRAINT "request_translation_pkey" PRIMARY KEY("request_id","language_code")
);
--> statement-breakpoint
CREATE TABLE "users_translation" (
	"user_id" integer NOT NULL,
	"language_code" varchar(255) NOT NULL,
	"first_name" text,
	"last_name" text,
	"history" text,
	"questionnaire" text,
	CONSTRAINT "users_translation_pkey" PRIMARY KEY("user_id","language_code")
);
--> statement-breakpoint
CREATE TABLE "user_viewers" (
	"user_id" integer NOT NULL,
	"viewer_id" integer NOT NULL,
	"user_favorite" boolean DEFAULT false,
	"viewer_favorite" boolean DEFAULT false,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "user_viewers_pkey" PRIMARY KEY("user_id","viewer_id")
);
--> statement-breakpoint
CREATE TABLE "user_associations" (
	"user_id" integer NOT NULL,
	"buser_id" integer NOT NULL,
	"user_favorite" boolean DEFAULT false,
	"buser_favorite" boolean DEFAULT false,
	"ma_manage_orders" boolean DEFAULT false,
	"is_notify_on_capture" boolean DEFAULT false,
	"is_rpm_enabled" boolean DEFAULT false,
	"is_customized_vitals_thresholds" boolean DEFAULT false,
	"vitals_thresholds" text,
	"vitals_cron" text,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "user_associations_pkey" PRIMARY KEY("user_id","buser_id")
);
--> statement-breakpoint
ALTER TABLE "auth_provider" ADD CONSTRAINT "auth_provider_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_notes" ADD CONSTRAINT "consult_notes_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_notes" ADD CONSTRAINT "consult_notes_order_guid_fkey" FOREIGN KEY ("order_guid") REFERENCES "public"."telehealth_service_order"("order_guid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "educational_videos" ADD CONSTRAINT "educational_videos_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "encounters_values" ADD CONSTRAINT "encounters_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "requests" ADD CONSTRAINT "requests_object_id_fkey" FOREIGN KEY ("object_id") REFERENCES "public"."request_objects"("object_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "requests" ADD CONSTRAINT "requests_order_guid_fkey" FOREIGN KEY ("order_guid") REFERENCES "public"."telehealth_service_order"("order_guid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "requests" ADD CONSTRAINT "requests_requestee_id_fkey" FOREIGN KEY ("requestee_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "requests" ADD CONSTRAINT "requests_requestor_id_fkey" FOREIGN KEY ("requestor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "encryption_keys" ADD CONSTRAINT "encryption_keys_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "health_summaries_schedule" ADD CONSTRAINT "health_summaries_schedule_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "feed" ADD CONSTRAINT "feed_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "feed" ADD CONSTRAINT "feed_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promo_codes" ADD CONSTRAINT "promo_codes_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "drugs" ADD CONSTRAINT "drugs_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."drugs_category"("category_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "invitations" ADD CONSTRAINT "invitations_invitor_id_fkey" FOREIGN KEY ("invitor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "login_requests" ADD CONSTRAINT "login_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "favourite_drugs" ADD CONSTRAINT "favourite_drugs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "immunizations_values" ADD CONSTRAINT "immunizations_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medications_values" ADD CONSTRAINT "medications_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "mms_patients" ADD CONSTRAINT "mms_patients_pharmacy_id_fkey" FOREIGN KEY ("pharmacy_id") REFERENCES "public"."pharmacies"("pharmacy_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "mms_patients" ADD CONSTRAINT "mms_patients_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "mms_prescriptions" ADD CONSTRAINT "mms_prescriptions_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."mms_patients"("patient_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "onehealth_lab_orders" ADD CONSTRAINT "onehealth_lab_orders_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "patient_insurances" ADD CONSTRAINT "patient_insurances_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."mms_patients"("patient_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "permission_groups" ADD CONSTRAINT "permission_groups_associated_user_id_fkey" FOREIGN KEY ("associated_user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "permission_groups" ADD CONSTRAINT "permission_groups_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_answer_given_by_fkey" FOREIGN KEY ("answer_given_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_order" ADD CONSTRAINT "telehealth_service_order_pharmacy_id_fkey" FOREIGN KEY ("pharmacy_id") REFERENCES "public"."pharmacies"("pharmacy_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_callee_id_fkey" FOREIGN KEY ("callee_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_caller_id_fkey" FOREIGN KEY ("caller_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_order_guid_fkey" FOREIGN KEY ("order_guid") REFERENCES "public"."telehealth_service_order"("order_guid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "orders" ADD CONSTRAINT "orders_schedule_id_fkey" FOREIGN KEY ("schedule_id") REFERENCES "public"."schedules"("schedule_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_update_details_history" ADD CONSTRAINT "consult_update_details_history_service_order_id_fkey" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_update_details_history" ADD CONSTRAINT "consult_update_details_history_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_update_details_history" ADD CONSTRAINT "consult_update_details_history_service_order_id_fkey1" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_update_details_history" ADD CONSTRAINT "consult_update_details_history_service_order_id_fkey2" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_update_details_history" ADD CONSTRAINT "consult_update_details_history_service_order_id_fkey3" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_update_details_history" ADD CONSTRAINT "consult_update_details_history_service_order_id_fkey4" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "provider_license" ADD CONSTRAINT "provider_license_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "provider_license" ADD CONSTRAINT "provider_license_license_state_fkey" FOREIGN KEY ("license_state") REFERENCES "public"."states"("state_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "requests_log" ADD CONSTRAINT "requests_log_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medicine_service_pharmacy_mapping" ADD CONSTRAINT "medicine_service_pharmacy_mapping_medicine_id_fkey" FOREIGN KEY ("medicine_id") REFERENCES "public"."medicines"("medicine_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medicine_service_pharmacy_mapping" ADD CONSTRAINT "medicine_service_pharmacy_mapping_pharmacy_id_fkey" FOREIGN KEY ("pharmacy_id") REFERENCES "public"."pharmacies"("pharmacy_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medicine_service_pharmacy_mapping" ADD CONSTRAINT "medicine_service_pharmacy_mapping_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat_room_members" ADD CONSTRAINT "chat_room_members_user_id_users_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat_room_members" ADD CONSTRAINT "chat_room_members_room_id_chat_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_payee_user_id_fkey" FOREIGN KEY ("payee_user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_payer_user_id_fkey" FOREIGN KEY ("payer_user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "stripe_user_payment_details" ADD CONSTRAINT "stripe_user_payment_details_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_procedure_codes_mapping" ADD CONSTRAINT "telehealth_service_procedure_codes_mapping_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_procedure_codes_mapping" ADD CONSTRAINT "telehealth_service_procedure_codes_mappi_procedure_code_id_fkey" FOREIGN KEY ("procedure_code_id") REFERENCES "public"."procedure_codes"("procedure_code_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_questions" ADD CONSTRAINT "telehealth_service_questions_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_provider_mapping" ADD CONSTRAINT "telehealth_service_provider_mapping_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_provider_mapping" ADD CONSTRAINT "telehealth_service_provider_mapping_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "subscription_plans" ADD CONSTRAINT "subscription_plans_service_master_id_fkey" FOREIGN KEY ("service_master_id") REFERENCES "public"."telehealth_service_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "stripe_user_details" ADD CONSTRAINT "stripe_user_details_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "social_history_values" ADD CONSTRAINT "social_history_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_particlehealth" ADD CONSTRAINT "user_particlehealth_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_particlehealth" ADD CONSTRAINT "user_particlehealth_buser_id_fkey" FOREIGN KEY ("buser_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_details" ADD CONSTRAINT "user_details_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_educational_videos" ADD CONSTRAINT "user_educational_videos_assigned_by_fkey" FOREIGN KEY ("referred_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_educational_videos" ADD CONSTRAINT "user_educational_videos_assigned_to_fkey" FOREIGN KEY ("referred_for") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_educational_videos" ADD CONSTRAINT "user_educational_videos_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_educational_videos" ADD CONSTRAINT "user_educational_videos_video_id_fkey" FOREIGN KEY ("video_id") REFERENCES "public"."educational_videos"("video_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_identities" ADD CONSTRAINT "user_identities_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "follow_up_reminder" ADD CONSTRAINT "follow_up_reminder_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "follow_up_reminder" ADD CONSTRAINT "follow_up_reminder_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "follow_up_reminder" ADD CONSTRAINT "follow_up_reminder_tennant_id_fkey" FOREIGN KEY ("tennant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "follow_up_reminder" ADD CONSTRAINT "follow_up_reminder_next_order_id_fkey" FOREIGN KEY ("next_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_file_repo_details" ADD CONSTRAINT "user_file_repo_details_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_health_summary" ADD CONSTRAINT "user_health_summary_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_subscription_billing" ADD CONSTRAINT "user_subscription_billing_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "public"."subscription_plans"("plan_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_subscription_billing" ADD CONSTRAINT "user_subscription_billing_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "drug_days" ADD CONSTRAINT "drug_days_drug_id_fkey" FOREIGN KEY ("drug_id") REFERENCES "public"."drugs"("drug_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_order_files" ADD CONSTRAINT "consult_order_files_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_order_files" ADD CONSTRAINT "consult_order_files_tennant_id_fkey" FOREIGN KEY ("tennant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_subscription" ADD CONSTRAINT "user_subscription_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "public"."subscription_plans"("plan_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_subscription" ADD CONSTRAINT "user_subscription_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_subscription" ADD CONSTRAINT "user_subscription_service_master_id_fkey" FOREIGN KEY ("service_master_id") REFERENCES "public"."telehealth_service_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_template" ADD CONSTRAINT "sms_template_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_vitals_documents" ADD CONSTRAINT "user_vitals_documents_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_vitals_documents" ADD CONSTRAINT "user_vitals_documents_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_sender_id_users_user_id_fk" FOREIGN KEY ("sender_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_room_id_chat_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "jobs" ADD CONSTRAINT "jobs_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "jobs" ADD CONSTRAINT "jobs_referral_id_fkey" FOREIGN KEY ("referral_id") REFERENCES "public"."referrals"("referral_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_schedules" ADD CONSTRAINT "user_schedules_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_schedules" ADD CONSTRAINT "user_schedules_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_schedules" ADD CONSTRAINT "user_schedules_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_schedules" ADD CONSTRAINT "user_schedules_deleted_by_fkey" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat_files" ADD CONSTRAINT "chat_files_user_id_users_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "chat_files" ADD CONSTRAINT "chat_files_room_id_chat_rooms_id_fk" FOREIGN KEY ("room_id") REFERENCES "public"."chat_rooms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "email_template" ADD CONSTRAINT "email_template_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prescription_preference" ADD CONSTRAINT "prescription_preference_pharmacy_id_fkey" FOREIGN KEY ("pharmacy_id") REFERENCES "public"."pharmacies"("pharmacy_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prescription_preference" ADD CONSTRAINT "prescription_preference_tennant_id_fkey" FOREIGN KEY ("tennant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lifefile_configuration" ADD CONSTRAINT "lifefile_configuration_tennant_id_fkey" FOREIGN KEY ("tennant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lifefile_configuration" ADD CONSTRAINT "lifefile_configuration_pharmacy_id_fkey" FOREIGN KEY ("pharmacy_id") REFERENCES "public"."pharmacies"("pharmacy_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tennant_master" ADD CONSTRAINT "tennant_master_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tennant_master" ADD CONSTRAINT "tennant_master_default_provider_id_fkey" FOREIGN KEY ("default_provider_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_reassign_history" ADD CONSTRAINT "consult_reassign_history_service_order_id_fkey" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_reassign_history" ADD CONSTRAINT "consult_reassign_history_previous_provider_fkey" FOREIGN KEY ("previous_provider") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_reassign_history" ADD CONSTRAINT "consult_reassign_history_updated_provider_fkey" FOREIGN KEY ("updated_provider") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consult_reassign_history" ADD CONSTRAINT "consult_reassign_history_reassigned_by_fkey" FOREIGN KEY ("reassigned_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_question_answer_dump" ADD CONSTRAINT "telehealth_service_question_answer_dump_service_order_id_fkey" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "external_requests_log" ADD CONSTRAINT "external_requests_log_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "mms_patient_invitations" ADD CONSTRAINT "mms_patient_invitations_mms_patient_id_fkey" FOREIGN KEY ("mms_patient_id") REFERENCES "public"."mms_patients"("patient_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_payment_mapping" ADD CONSTRAINT "service_payment_mapping_service_master_id_fkey" FOREIGN KEY ("service_master_id") REFERENCES "public"."telehealth_service_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_payment_mapping" ADD CONSTRAINT "service_payment_mapping_payment_details_id_fkey" FOREIGN KEY ("payment_details_id") REFERENCES "public"."payment_details"("payment_details_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_tennant_id_fkey" FOREIGN KEY ("tennant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referral_tracking" ADD CONSTRAINT "referral_tracking_referral_id_fkey" FOREIGN KEY ("referral_id") REFERENCES "public"."referrals"("referral_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referral_tracking" ADD CONSTRAINT "referral_tracking_order_guid_fkey" FOREIGN KEY ("order_guid") REFERENCES "public"."telehealth_service_order"("order_guid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "support_notes" ADD CONSTRAINT "support_notes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "support_notes" ADD CONSTRAINT "support_notes_support_user_id_fkey" FOREIGN KEY ("support_user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "support_notes" ADD CONSTRAINT "support_notes_order_guid_fkey" FOREIGN KEY ("order_guid") REFERENCES "public"."telehealth_service_order"("order_guid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tenant_auth_provider" ADD CONSTRAINT "tenant_auth_provider_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "tennant_config" ADD CONSTRAINT "tennant_config_tennant_id_fkey" FOREIGN KEY ("tennant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_action_preference" ADD CONSTRAINT "service_action_preference_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "service_action_preference" ADD CONSTRAINT "service_action_preference_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_master" ADD CONSTRAINT "telehealth_service_master_tennant_id_fkey" FOREIGN KEY ("tennant_id") REFERENCES "public"."tennant_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_master" ADD CONSTRAINT "telehealth_service_master_initial_service_id_fkey" FOREIGN KEY ("initial_service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_services" ADD CONSTRAINT "telehealth_services_service_master_id_fkey" FOREIGN KEY ("service_master_id") REFERENCES "public"."telehealth_service_master"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_services" ADD CONSTRAINT "telehealth_services_next_service_id_fkey" FOREIGN KEY ("next_service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "diagnoses_values" ADD CONSTRAINT "diagnoses_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "family_history_values" ADD CONSTRAINT "family_history_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "procedures_values" ADD CONSTRAINT "procedures_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "care_plan_values" ADD CONSTRAINT "care_plan_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "results_values" ADD CONSTRAINT "results_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "allergies_values" ADD CONSTRAINT "allergies_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_values" ADD CONSTRAINT "document_values_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "public"."user_health_summary"("summary_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "mms_prescription_refills" ADD CONSTRAINT "mms_prescription_refills_prescription_id_fkey" FOREIGN KEY ("prescription_id") REFERENCES "public"."mms_prescriptions"("prescription_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vitals_summary_upload_status" ADD CONSTRAINT "vitals_summary_upload_status_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vitals_summary_upload_status" ADD CONSTRAINT "vitals_summary_upload_status_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vitals_notification_cycle" ADD CONSTRAINT "vitals_notification_cycle_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "visit_summary_upload_status" ADD CONSTRAINT "visit_summary_upload_status_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("order_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "visit_summary_upload_status" ADD CONSTRAINT "visit_summary_upload_status_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vitals_patient_monitoring" ADD CONSTRAINT "vitals_patient_monitoring_buser_id_fkey" FOREIGN KEY ("buser_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vitals_patient_monitoring" ADD CONSTRAINT "vitals_patient_monitoring_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_forms" ADD CONSTRAINT "user_forms_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_forms" ADD CONSTRAINT "user_forms_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_forms" ADD CONSTRAINT "user_forms_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_forms" ADD CONSTRAINT "user_forms_form_id_fkey" FOREIGN KEY ("form_id") REFERENCES "public"."forms"("form_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_forms" ADD CONSTRAINT "user_forms_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_files" ADD CONSTRAINT "user_files_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_files" ADD CONSTRAINT "user_files_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_files" ADD CONSTRAINT "user_files_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_files" ADD CONSTRAINT "user_files_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_diet" ADD CONSTRAINT "user_diet_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_diet" ADD CONSTRAINT "user_diet_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_question_answer" ADD CONSTRAINT "telehealth_service_question_answer_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "public"."telehealth_service_questions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_question_answer" ADD CONSTRAINT "telehealth_service_question_answer_service_order_id_fkey" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "faxes" ADD CONSTRAINT "faxes_referral_id_fkey" FOREIGN KEY ("referral_id") REFERENCES "public"."referrals"("referral_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "faxes" ADD CONSTRAINT "faxes_sent_by_fkey" FOREIGN KEY ("sent_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "faxes" ADD CONSTRAINT "faxes_sent_for_fkey" FOREIGN KEY ("sent_for") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_given_by_fkey" FOREIGN KEY ("given_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_given_to_fkey" FOREIGN KEY ("given_to") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("order_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referrals" ADD CONSTRAINT "referrals_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referrals" ADD CONSTRAINT "referrals_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referrals" ADD CONSTRAINT "referrals_referred_by_fkey" FOREIGN KEY ("referred_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referrals" ADD CONSTRAINT "referrals_referred_for_fkey" FOREIGN KEY ("referred_for") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referrals" ADD CONSTRAINT "referrals_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("product_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "transcriptions" ADD CONSTRAINT "transcriptions_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("order_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "transcriptions" ADD CONSTRAINT "transcriptions_tuser_id_fkey" FOREIGN KEY ("tuser_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_vitals" ADD CONSTRAINT "user_vitals_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_vitals" ADD CONSTRAINT "user_vitals_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "provider_ratings" ADD CONSTRAINT "provider_ratings_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."orders"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "provider_ratings" ADD CONSTRAINT "provider_ratings_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "provider_ratings" ADD CONSTRAINT "provider_ratings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schedules" ADD CONSTRAINT "schedules_order_guid_fkey" FOREIGN KEY ("order_guid") REFERENCES "public"."telehealth_service_order"("order_guid") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schedules" ADD CONSTRAINT "schedules_scheduled_by_fkey" FOREIGN KEY ("scheduled_by") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schedules" ADD CONSTRAINT "schedules_scheduled_with_fkey" FOREIGN KEY ("scheduled_with") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schedules" ADD CONSTRAINT "schedules_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "health_summaries_log" ADD CONSTRAINT "health_summaries_log_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "conversation_messages" ADD CONSTRAINT "conversation_messages_c_id_fkey" FOREIGN KEY ("c_id") REFERENCES "public"."conversations"("c_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "conversation_messages" ADD CONSTRAINT "conversation_messages_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_user_one_fkey" FOREIGN KEY ("user_one") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_user_two_fkey" FOREIGN KEY ("user_two") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prescription_transfer_medications" ADD CONSTRAINT "prescription_transfer_medications_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."prescription_transfer_request"("request_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prescription_transfer_request" ADD CONSTRAINT "prescription_transfer_request_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "refill_request" ADD CONSTRAINT "refill_request_service_order_id_fkey" FOREIGN KEY ("service_order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_state_mapping" ADD CONSTRAINT "telehealth_service_state_mapping_state_id_fkey" FOREIGN KEY ("state_id") REFERENCES "public"."states"("state_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "telehealth_service_state_mapping" ADD CONSTRAINT "telehealth_service_state_mapping_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pharmacy_state_service_mapping" ADD CONSTRAINT "pharmacy_state_service_mapping_service_id_fkey" FOREIGN KEY ("service_id") REFERENCES "public"."telehealth_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pharmacy_state_service_mapping" ADD CONSTRAINT "pharmacy_state_service_mapping_state_id_fkey" FOREIGN KEY ("state_id") REFERENCES "public"."states"("state_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pharmacy_state_service_mapping" ADD CONSTRAINT "pharmacy_state_service_mapping_pharmacy_id_fkey" FOREIGN KEY ("pharmacy_id") REFERENCES "public"."pharmacies"("pharmacy_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_insurance" ADD CONSTRAINT "user_insurance_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "insurance_eligibility_logs" ADD CONSTRAINT "insurance_eligibility_logs_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "public"."telehealth_service_order"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_practice_groups" ADD CONSTRAINT "user_practice_groups_practice_group_id_fkey" FOREIGN KEY ("practice_group_id") REFERENCES "public"."practice_groups"("practice_group_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_practice_groups" ADD CONSTRAINT "user_practice_groups_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "schedule_translation" ADD CONSTRAINT "schedule_translation_schedule_id_fkey" FOREIGN KEY ("schedule_id") REFERENCES "public"."schedules"("schedule_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "request_translation" ADD CONSTRAINT "request_translation_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."requests"("request_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "users_translation" ADD CONSTRAINT "users_translation_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_viewers" ADD CONSTRAINT "user_viewers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_viewers" ADD CONSTRAINT "user_viewers_viewer_id_fkey" FOREIGN KEY ("viewer_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_associations" ADD CONSTRAINT "user_associations_buser_id_fkey" FOREIGN KEY ("buser_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_associations" ADD CONSTRAINT "user_associations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_user_vitals_user_id_date" ON "user_vitals" USING btree ("user_id" int4_ops,"date" int4_ops);--> statement-breakpoint
CREATE INDEX "refill_request_drug_details" ON "refill_request" USING btree ("drug_details" text_ops);--> statement-breakpoint
CREATE VIEW "public"."dashboard_tennant_list" AS (SELECT tennant_master.id, tennant_master.tennant_name, sum( CASE WHEN telehealth_service_order.service_type = 'ASYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS async_count, sum( CASE WHEN telehealth_service_order.service_type = 'SYNC'::enum_telehealth_service_order_service_type THEN 1 ELSE 0 END) AS sync_count, count(telehealth_service_order.id) AS total_orders FROM tennant_master JOIN users ON tennant_master.id = users.tennant_id JOIN telehealth_service_order ON users.user_id = telehealth_service_order.answer_given_by GROUP BY tennant_master.id, tennant_master.tennant_name ORDER BY tennant_master.id);
*/