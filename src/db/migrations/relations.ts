import { relations } from 'drizzle-orm/relations';
import {
  users,
  authProvider,
  orders,
  consultNotes,
  telehealthServiceOrder,
  educationalVideos,
  userHealthSummary,
  encountersValues,
  requestObjects,
  requests,
  encryptionKeys,
  healthSummariesSchedule,
  feed,
  telehealthServices,
  promoCodes,
  drugsCategory,
  drugs,
  invitations,
  loginRequests,
  favouriteDrugs,
  immunizationsValues,
  medicationsValues,
  pharmacies,
  mmsPatients,
  mmsPrescriptions,
  onehealthLabOrders,
  patientInsurances,
  permissionGroups,
  schedules,
  consultUpdateDetailsHistory,
  providerLicense,
  states,
  requestsLog,
  medicines,
  medicineServicePharmacyMapping,
  chatRoomMembers,
  chatRooms,
  telehealthServiceMaster,
  subscriptionPlans,
  stripeUserPaymentDetails,
  telehealthServiceProcedureCodesMapping,
  procedureCodes,
  telehealthServiceQuestions,
  telehealthServiceProviderMapping,
  transactions,
  paymentDetails,
  stripeUserDetails,
  socialHistoryValues,
  userParticlehealth,
  userDetails,
  userEducationalVideos,
  userIdentities,
  followUpReminder,
  tennantMaster,
  userFileRepoDetails,
  userSubscriptionBilling,
  drugDays,
  userSubscription,
  consultOrderFiles,
  smsTemplate,
  userVitalsDocuments,
  chatMessages,
  jobs,
  referrals,
  userSchedules,
  chatFiles,
  emailTemplate,
  prescriptionPreference,
  lifefileConfiguration,
  telehealthServiceQuestionAnswerDump,
  consultReassignHistory,
  externalRequestsLog,
  mmsPatientInvitations,
  servicePaymentMapping,
  referralTracking,
  supportNotes,
  tennantConfig,
  tenantAuthProvider,
  serviceActionPreference,
  diagnosesValues,
  familyHistoryValues,
  proceduresValues,
  carePlanValues,
  resultsValues,
  allergiesValues,
  documentValues,
  mmsPrescriptionRefills,
  vitalsSummaryUploadStatus,
  vitalsNotificationCycle,
  visitSummaryUploadStatus,
  vitalsPatientMonitoring,
  userForms,
  forms,
  userDiet,
  telehealthServiceQuestionAnswer,
  faxes,
  reviews,
  products,
  transcriptions,
  userVitals,
  providerRatings,
  healthSummariesLog,
  conversations,
  conversationMessages,
  prescriptionTransferRequest,
  prescriptionTransferMedications,
  refillRequest,
  telehealthServiceStateMapping,
  pharmacyStateServiceMapping,
  userInsurance,
  insuranceEligibilityLogs,
  userExternalIdMapping,
  userFiles,
  consultNoteTemplates,
  tenantFiles,
  telehealthServiceCategory,
  userLicenses,
  notificationReminder,
  practiceGroups,
  userPracticeGroups,
  scheduleTranslation,
  requestTranslation,
  usersTranslation,
  userViewers,
  userAssociations
} from './schema';

export const authProviderRelations = relations(authProvider, ({ one }) => ({
  user: one(users, {
    fields: [authProvider.userId],
    references: [users.userId]
  })
}));

export const usersRelations = relations(users, ({ one, many }) => ({
  authProviders: many(authProvider),
  educationalVideos: many(educationalVideos),
  requests_requesteeId: many(requests, {
    relationName: 'requests_requesteeId_users_userId'
  }),
  requests_requestorId: many(requests, {
    relationName: 'requests_requestorId_users_userId'
  }),
  encryptionKeys: many(encryptionKeys),
  healthSummariesSchedules: many(healthSummariesSchedule),
  feeds_createdBy: many(feed, {
    relationName: 'feed_createdBy_users_userId'
  }),
  feeds_userId: many(feed, {
    relationName: 'feed_userId_users_userId'
  }),
  invitations: many(invitations),
  loginRequests: many(loginRequests),
  favouriteDrugs: many(favouriteDrugs),
  mmsPatients: many(mmsPatients),
  onehealthLabOrders: many(onehealthLabOrders),
  permissionGroups_associatedUserId: many(permissionGroups, {
    relationName: 'permissionGroups_associatedUserId_users_userId'
  }),
  permissionGroups_patientId: many(permissionGroups, {
    relationName: 'permissionGroups_patientId_users_userId'
  }),
  telehealthServiceOrders_answerGivenBy: many(telehealthServiceOrder, {
    relationName: 'telehealthServiceOrder_answerGivenBy_users_userId'
  }),
  telehealthServiceOrders_providerId: many(telehealthServiceOrder, {
    relationName: 'telehealthServiceOrder_providerId_users_userId'
  }),
  orders_calleeId: many(orders, {
    relationName: 'orders_calleeId_users_userId'
  }),
  orders_callerId: many(orders, {
    relationName: 'orders_callerId_users_userId'
  }),
  orders_doctorId: many(orders, {
    relationName: 'orders_doctorId_users_userId'
  }),
  consultUpdateDetailsHistories: many(consultUpdateDetailsHistory),
  providerLicenses: many(providerLicense),
  requestsLogs: many(requestsLog),
  chatRoomMembers: many(chatRoomMembers),
  subscriptionPlans: many(subscriptionPlans),
  stripeUserPaymentDetails: many(stripeUserPaymentDetails),
  telehealthServiceProviderMappings: many(telehealthServiceProviderMapping),
  transactions_payeeUserId: many(transactions, {
    relationName: 'transactions_payeeUserId_users_userId'
  }),
  transactions_payerUserId: many(transactions, {
    relationName: 'transactions_payerUserId_users_userId'
  }),
  stripeUserDetails: many(stripeUserDetails),
  userParticlehealths_userId: many(userParticlehealth, {
    relationName: 'userParticlehealth_userId_users_userId'
  }),
  userParticlehealths_buserId: many(userParticlehealth, {
    relationName: 'userParticlehealth_buserId_users_userId'
  }),
  userDetails: many(userDetails),
  userEducationalVideos_referredBy: many(userEducationalVideos, {
    relationName: 'userEducationalVideos_referredBy_users_userId'
  }),
  userEducationalVideos_referredFor: many(userEducationalVideos, {
    relationName: 'userEducationalVideos_referredFor_users_userId'
  }),
  userEducationalVideos_doctorId: many(userEducationalVideos, {
    relationName: 'userEducationalVideos_doctorId_users_userId'
  }),
  userIdentities: many(userIdentities),
  followUpReminders: many(followUpReminder),
  userFileRepoDetails: many(userFileRepoDetails),
  userHealthSummaries: many(userHealthSummary),
  userSubscriptionBillings: many(userSubscriptionBilling),
  userSubscriptions: many(userSubscription),
  userVitalsDocuments_doctorId: many(userVitalsDocuments, {
    relationName: 'userVitalsDocuments_doctorId_users_userId'
  }),
  userVitalsDocuments_userId: many(userVitalsDocuments, {
    relationName: 'userVitalsDocuments_userId_users_userId'
  }),
  chatMessages: many(chatMessages),
  userSchedules_userId: many(userSchedules, {
    relationName: 'userSchedules_userId_users_userId'
  }),
  userSchedules_createdBy: many(userSchedules, {
    relationName: 'userSchedules_createdBy_users_userId'
  }),
  userSchedules_updatedBy: many(userSchedules, {
    relationName: 'userSchedules_updatedBy_users_userId'
  }),
  userSchedules_deletedBy: many(userSchedules, {
    relationName: 'userSchedules_deletedBy_users_userId'
  }),
  chatFiles: many(chatFiles),
  telehealthServiceQuestionAnswerDumps: many(
    telehealthServiceQuestionAnswerDump
  ),
  tennantMasters: many(tennantMaster, {
    relationName: 'tennantMaster_defaultProviderId_users_userId'
  }),
  consultReassignHistories_previousProvider: many(consultReassignHistory, {
    relationName: 'consultReassignHistory_previousProvider_users_userId'
  }),
  consultReassignHistories_updatedProvider: many(consultReassignHistory, {
    relationName: 'consultReassignHistory_updatedProvider_users_userId'
  }),
  consultReassignHistories_reassignedBy: many(consultReassignHistory, {
    relationName: 'consultReassignHistory_reassignedBy_users_userId'
  }),
  tennantMaster: one(tennantMaster, {
    fields: [users.tennantId],
    references: [tennantMaster.id],
    relationName: 'users_tennantId_tennantMaster_id'
  }),
  user: one(users, {
    fields: [users.parentId],
    references: [users.userId],
    relationName: 'users_parentId_users_userId'
  }),
  users: many(users, {
    relationName: 'users_parentId_users_userId'
  }),
  externalRequestsLogs: many(externalRequestsLog),
  supportNotes_userId: many(supportNotes, {
    relationName: 'supportNotes_userId_users_userId'
  }),
  supportNotes_supportUserId: many(supportNotes, {
    relationName: 'supportNotes_supportUserId_users_userId'
  }),
  vitalsSummaryUploadStatuses_doctorId: many(vitalsSummaryUploadStatus, {
    relationName: 'vitalsSummaryUploadStatus_doctorId_users_userId'
  }),
  vitalsSummaryUploadStatuses_patientId: many(vitalsSummaryUploadStatus, {
    relationName: 'vitalsSummaryUploadStatus_patientId_users_userId'
  }),
  vitalsNotificationCycles: many(vitalsNotificationCycle),
  visitSummaryUploadStatuses: many(visitSummaryUploadStatus),
  vitalsPatientMonitorings_buserId: many(vitalsPatientMonitoring, {
    relationName: 'vitalsPatientMonitoring_buserId_users_userId'
  }),
  vitalsPatientMonitorings_patientId: many(vitalsPatientMonitoring, {
    relationName: 'vitalsPatientMonitoring_patientId_users_userId'
  }),
  userForms_assignedBy: many(userForms, {
    relationName: 'userForms_assignedBy_users_userId'
  }),
  userForms_assignedTo: many(userForms, {
    relationName: 'userForms_assignedTo_users_userId'
  }),
  userForms_doctorId: many(userForms, {
    relationName: 'userForms_doctorId_users_userId'
  }),
  userDiets: many(userDiet),
  faxes_sentBy: many(faxes, {
    relationName: 'faxes_sentBy_users_userId'
  }),
  faxes_sentFor: many(faxes, {
    relationName: 'faxes_sentFor_users_userId'
  }),
  reviews_givenBy: many(reviews, {
    relationName: 'reviews_givenBy_users_userId'
  }),
  reviews_givenTo: many(reviews, {
    relationName: 'reviews_givenTo_users_userId'
  }),
  referrals_doctorId: many(referrals, {
    relationName: 'referrals_doctorId_users_userId'
  }),
  referrals_referredBy: many(referrals, {
    relationName: 'referrals_referredBy_users_userId'
  }),
  referrals_referredFor: many(referrals, {
    relationName: 'referrals_referredFor_users_userId'
  }),
  transcriptions: many(transcriptions),
  userVitals: many(userVitals),
  providerRatings_providerId: many(providerRatings, {
    relationName: 'providerRatings_providerId_users_userId'
  }),
  providerRatings_userId: many(providerRatings, {
    relationName: 'providerRatings_userId_users_userId'
  }),
  schedules_scheduledBy: many(schedules, {
    relationName: 'schedules_scheduledBy_users_userId'
  }),
  schedules_scheduledWith: many(schedules, {
    relationName: 'schedules_scheduledWith_users_userId'
  }),
  healthSummariesLogs: many(healthSummariesLog),
  conversationMessages: many(conversationMessages),
  conversations_userOne: many(conversations, {
    relationName: 'conversations_userOne_users_userId'
  }),
  conversations_userTwo: many(conversations, {
    relationName: 'conversations_userTwo_users_userId'
  }),
  prescriptionTransferRequests: many(prescriptionTransferRequest),
  userInsurances: many(userInsurance),
  userExternalIdMappings: many(userExternalIdMapping),
  userFiles_createdBy: many(userFiles, {
    relationName: 'userFiles_createdBy_users_userId'
  }),
  userFiles_doctorId: many(userFiles, {
    relationName: 'userFiles_doctorId_users_userId'
  }),
  userFiles_userId: many(userFiles, {
    relationName: 'userFiles_userId_users_userId'
  }),
  consultNoteTemplates: many(consultNoteTemplates),
  userLicenses: many(userLicenses),
  notificationReminders: many(notificationReminder),
  userPracticeGroups: many(userPracticeGroups),
  usersTranslations: many(usersTranslation),
  userViewers_userId: many(userViewers, {
    relationName: 'userViewers_userId_users_userId'
  }),
  userViewers_viewerId: many(userViewers, {
    relationName: 'userViewers_viewerId_users_userId'
  }),
  userAssociations_buserId: many(userAssociations, {
    relationName: 'userAssociations_buserId_users_userId'
  }),
  userAssociations_userId: many(userAssociations, {
    relationName: 'userAssociations_userId_users_userId'
  })
}));

export const consultNotesRelations = relations(consultNotes, ({ one }) => ({
  order: one(orders, {
    fields: [consultNotes.orderId],
    references: [orders.id]
  }),
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [consultNotes.orderGuid],
    references: [telehealthServiceOrder.orderGuid]
  })
}));

export const ordersRelations = relations(orders, ({ one, many }) => ({
  consultNotes: many(consultNotes),
  telehealthServiceOrders: many(telehealthServiceOrder, {
    relationName: 'telehealthServiceOrder_orderId_orders_id'
  }),
  user_calleeId: one(users, {
    fields: [orders.calleeId],
    references: [users.userId],
    relationName: 'orders_calleeId_users_userId'
  }),
  user_callerId: one(users, {
    fields: [orders.callerId],
    references: [users.userId],
    relationName: 'orders_callerId_users_userId'
  }),
  user_doctorId: one(users, {
    fields: [orders.doctorId],
    references: [users.userId],
    relationName: 'orders_doctorId_users_userId'
  }),
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [orders.orderGuid],
    references: [telehealthServiceOrder.orderGuid],
    relationName: 'orders_orderGuid_telehealthServiceOrder_orderGuid'
  }),
  schedule: one(schedules, {
    fields: [orders.scheduleId],
    references: [schedules.scheduleId]
  }),
  visitSummaryUploadStatuses: many(visitSummaryUploadStatus),
  userForms: many(userForms),
  userDiets: many(userDiet),
  reviews: many(reviews),
  referrals: many(referrals),
  transcriptions: many(transcriptions),
  userVitals: many(userVitals),
  providerRatings: many(providerRatings),
  userFiles: many(userFiles)
}));

export const telehealthServiceOrderRelations = relations(
  telehealthServiceOrder,
  ({ one, many }) => ({
    consultNotes: many(consultNotes),
    requests: many(requests),
    user_answerGivenBy: one(users, {
      fields: [telehealthServiceOrder.answerGivenBy],
      references: [users.userId],
      relationName: 'telehealthServiceOrder_answerGivenBy_users_userId'
    }),
    order: one(orders, {
      fields: [telehealthServiceOrder.orderId],
      references: [orders.id],
      relationName: 'telehealthServiceOrder_orderId_orders_id'
    }),
    user_providerId: one(users, {
      fields: [telehealthServiceOrder.providerId],
      references: [users.userId],
      relationName: 'telehealthServiceOrder_providerId_users_userId'
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceOrder.serviceId],
      references: [telehealthServices.id]
    }),
    pharmacy: one(pharmacies, {
      fields: [telehealthServiceOrder.pharmacyId],
      references: [pharmacies.pharmacyId]
    }),
    orders: many(orders, {
      relationName: 'orders_orderGuid_telehealthServiceOrder_orderGuid'
    }),
    consultUpdateDetailsHistories_serviceOrderId: many(
      consultUpdateDetailsHistory,
      {
        relationName:
          'consultUpdateDetailsHistory_serviceOrderId_telehealthServiceOrder_id'
      }
    ),

    followUpReminders_orderId: many(followUpReminder, {
      relationName: 'followUpReminder_orderId_telehealthServiceOrder_id'
    }),
    followUpReminders_nextOrderId: many(followUpReminder, {
      relationName: 'followUpReminder_nextOrderId_telehealthServiceOrder_id'
    }),
    consultOrderFiles: many(consultOrderFiles),
    jobs: many(jobs),
    telehealthServiceQuestionAnswerDumps: many(
      telehealthServiceQuestionAnswerDump
    ),
    consultReassignHistories: many(consultReassignHistory),
    referralTrackings: many(referralTracking),
    supportNotes: many(supportNotes),
    telehealthServiceQuestionAnswers: many(telehealthServiceQuestionAnswer),
    schedules_orderGuid: many(schedules, {
      relationName: 'schedules_orderGuid_telehealthServiceOrder_orderGuid'
    }),
    schedules_orderId: many(schedules, {
      relationName: 'schedules_orderId_telehealthServiceOrder_id'
    }),
    refillRequests: many(refillRequest),
    insuranceEligibilityLogs: many(insuranceEligibilityLogs),
    notificationReminders: many(notificationReminder)
  })
);

export const educationalVideosRelations = relations(
  educationalVideos,
  ({ one, many }) => ({
    user: one(users, {
      fields: [educationalVideos.createdBy],
      references: [users.userId]
    }),
    userEducationalVideos: many(userEducationalVideos)
  })
);

export const encountersValuesRelations = relations(
  encountersValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [encountersValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);

export const userHealthSummaryRelations = relations(
  userHealthSummary,
  ({ one, many }) => ({
    encountersValues: many(encountersValues),
    immunizationsValues: many(immunizationsValues),
    medicationsValues: many(medicationsValues),
    socialHistoryValues: many(socialHistoryValues),
    user: one(users, {
      fields: [userHealthSummary.userId],
      references: [users.userId]
    }),
    diagnosesValues: many(diagnosesValues),
    familyHistoryValues: many(familyHistoryValues),
    proceduresValues: many(proceduresValues),
    carePlanValues: many(carePlanValues),
    resultsValues: many(resultsValues),
    allergiesValues: many(allergiesValues),
    documentValues: many(documentValues)
  })
);

export const requestsRelations = relations(requests, ({ one, many }) => ({
  requestObject: one(requestObjects, {
    fields: [requests.objectId],
    references: [requestObjects.objectId]
  }),
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [requests.orderGuid],
    references: [telehealthServiceOrder.orderGuid]
  }),
  user_requesteeId: one(users, {
    fields: [requests.requesteeId],
    references: [users.userId],
    relationName: 'requests_requesteeId_users_userId'
  }),
  user_requestorId: one(users, {
    fields: [requests.requestorId],
    references: [users.userId],
    relationName: 'requests_requestorId_users_userId'
  }),
  requestTranslations: many(requestTranslation)
}));

export const requestObjectsRelations = relations(
  requestObjects,
  ({ many }) => ({
    requests: many(requests)
  })
);

export const encryptionKeysRelations = relations(encryptionKeys, ({ one }) => ({
  user: one(users, {
    fields: [encryptionKeys.userId],
    references: [users.userId]
  })
}));

export const healthSummariesScheduleRelations = relations(
  healthSummariesSchedule,
  ({ one }) => ({
    user: one(users, {
      fields: [healthSummariesSchedule.userId],
      references: [users.userId]
    })
  })
);

export const feedRelations = relations(feed, ({ one }) => ({
  user_createdBy: one(users, {
    fields: [feed.createdBy],
    references: [users.userId],
    relationName: 'feed_createdBy_users_userId'
  }),
  user_userId: one(users, {
    fields: [feed.userId],
    references: [users.userId],
    relationName: 'feed_userId_users_userId'
  })
}));

export const promoCodesRelations = relations(promoCodes, ({ one }) => ({
  telehealthService: one(telehealthServices, {
    fields: [promoCodes.serviceId],
    references: [telehealthServices.id]
  })
}));

export const telehealthServicesRelations = relations(
  telehealthServices,
  ({ one, many }) => ({
    promoCodes: many(promoCodes),
    telehealthServiceOrders: many(telehealthServiceOrder),
    medicineServicePharmacyMappings: many(medicineServicePharmacyMapping),
    telehealthServiceProcedureCodesMappings: many(
      telehealthServiceProcedureCodesMapping
    ),
    telehealthServiceQuestions: many(telehealthServiceQuestions),
    telehealthServiceProviderMappings: many(telehealthServiceProviderMapping),
    smsTemplates: many(smsTemplate),
    emailTemplates: many(emailTemplate),
    telehealthServiceMaster: one(telehealthServiceMaster, {
      fields: [telehealthServices.serviceMasterId],
      references: [telehealthServiceMaster.id],
      relationName:
        'telehealthServices_serviceMasterId_telehealthServiceMaster_id'
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServices.nextServiceId],
      references: [telehealthServices.id],
      relationName: 'telehealthServices_nextServiceId_telehealthServices_id'
    }),
    telehealthServices: many(telehealthServices, {
      relationName: 'telehealthServices_nextServiceId_telehealthServices_id'
    }),
    telehealthServiceMasters: many(telehealthServiceMaster, {
      relationName:
        'telehealthServiceMaster_initialServiceId_telehealthServices_id'
    }),
    serviceActionPreferences: many(serviceActionPreference),
    telehealthServiceStateMappings: many(telehealthServiceStateMapping),
    pharmacyStateServiceMappings: many(pharmacyStateServiceMapping),
    tenantFiles: many(tenantFiles)
  })
);

export const drugsRelations = relations(drugs, ({ one, many }) => ({
  drugsCategory: one(drugsCategory, {
    fields: [drugs.categoryId],
    references: [drugsCategory.categoryId]
  }),
  drugDays: many(drugDays)
}));

export const drugsCategoryRelations = relations(drugsCategory, ({ many }) => ({
  drugs: many(drugs)
}));

export const invitationsRelations = relations(invitations, ({ one }) => ({
  user: one(users, {
    fields: [invitations.invitorId],
    references: [users.userId]
  })
}));

export const loginRequestsRelations = relations(loginRequests, ({ one }) => ({
  user: one(users, {
    fields: [loginRequests.userId],
    references: [users.userId]
  })
}));

export const favouriteDrugsRelations = relations(favouriteDrugs, ({ one }) => ({
  user: one(users, {
    fields: [favouriteDrugs.userId],
    references: [users.userId]
  })
}));

export const immunizationsValuesRelations = relations(
  immunizationsValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [immunizationsValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);

export const medicationsValuesRelations = relations(
  medicationsValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [medicationsValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);

export const mmsPatientsRelations = relations(mmsPatients, ({ one, many }) => ({
  pharmacy: one(pharmacies, {
    fields: [mmsPatients.pharmacyId],
    references: [pharmacies.pharmacyId]
  }),
  user: one(users, {
    fields: [mmsPatients.userId],
    references: [users.userId]
  }),
  mmsPrescriptions: many(mmsPrescriptions),
  patientInsurances: many(patientInsurances),
  mmsPatientInvitations: many(mmsPatientInvitations)
}));

export const pharmaciesRelations = relations(pharmacies, ({ many }) => ({
  mmsPatients: many(mmsPatients),
  telehealthServiceOrders: many(telehealthServiceOrder),
  medicineServicePharmacyMappings: many(medicineServicePharmacyMapping),
  prescriptionPreferences: many(prescriptionPreference),
  lifefileConfigurations: many(lifefileConfiguration),
  pharmacyStateServiceMappings: many(pharmacyStateServiceMapping)
}));

export const mmsPrescriptionsRelations = relations(
  mmsPrescriptions,
  ({ one, many }) => ({
    mmsPatient: one(mmsPatients, {
      fields: [mmsPrescriptions.patientId],
      references: [mmsPatients.patientId]
    }),
    mmsPrescriptionRefills: many(mmsPrescriptionRefills)
  })
);

export const onehealthLabOrdersRelations = relations(
  onehealthLabOrders,
  ({ one }) => ({
    user: one(users, {
      fields: [onehealthLabOrders.userId],
      references: [users.userId]
    })
  })
);

export const patientInsurancesRelations = relations(
  patientInsurances,
  ({ one }) => ({
    mmsPatient: one(mmsPatients, {
      fields: [patientInsurances.patientId],
      references: [mmsPatients.patientId]
    })
  })
);

export const permissionGroupsRelations = relations(
  permissionGroups,
  ({ one }) => ({
    user_associatedUserId: one(users, {
      fields: [permissionGroups.associatedUserId],
      references: [users.userId],
      relationName: 'permissionGroups_associatedUserId_users_userId'
    }),
    user_patientId: one(users, {
      fields: [permissionGroups.patientId],
      references: [users.userId],
      relationName: 'permissionGroups_patientId_users_userId'
    })
  })
);

export const schedulesRelations = relations(schedules, ({ one, many }) => ({
  orders: many(orders),
  telehealthServiceOrder_orderGuid: one(telehealthServiceOrder, {
    fields: [schedules.orderGuid],
    references: [telehealthServiceOrder.orderGuid],
    relationName: 'schedules_orderGuid_telehealthServiceOrder_orderGuid'
  }),
  user_scheduledBy: one(users, {
    fields: [schedules.scheduledBy],
    references: [users.userId],
    relationName: 'schedules_scheduledBy_users_userId'
  }),
  user_scheduledWith: one(users, {
    fields: [schedules.scheduledWith],
    references: [users.userId],
    relationName: 'schedules_scheduledWith_users_userId'
  }),
  telehealthServiceOrder_orderId: one(telehealthServiceOrder, {
    fields: [schedules.orderId],
    references: [telehealthServiceOrder.id],
    relationName: 'schedules_orderId_telehealthServiceOrder_id'
  }),
  scheduleTranslations: many(scheduleTranslation)
}));

export const consultUpdateDetailsHistoryRelations = relations(
  consultUpdateDetailsHistory,
  ({ one }) => ({
    telehealthServiceOrder_serviceOrderId: one(telehealthServiceOrder, {
      fields: [consultUpdateDetailsHistory.serviceOrderId],
      references: [telehealthServiceOrder.id],
      relationName:
        'consultUpdateDetailsHistory_serviceOrderId_telehealthServiceOrder_id'
    }),
    user: one(users, {
      fields: [consultUpdateDetailsHistory.userId],
      references: [users.userId]
    })
  })
);

export const providerLicenseRelations = relations(
  providerLicense,
  ({ one }) => ({
    user: one(users, {
      fields: [providerLicense.userId],
      references: [users.userId]
    }),
    state: one(states, {
      fields: [providerLicense.licenseState],
      references: [states.stateId]
    })
  })
);

export const statesRelations = relations(states, ({ many }) => ({
  providerLicenses: many(providerLicense),
  telehealthServiceStateMappings: many(telehealthServiceStateMapping),
  pharmacyStateServiceMappings: many(pharmacyStateServiceMapping),
  userLicenses: many(userLicenses)
}));

export const requestsLogRelations = relations(requestsLog, ({ one }) => ({
  user: one(users, {
    fields: [requestsLog.userId],
    references: [users.userId]
  })
}));

export const medicineServicePharmacyMappingRelations = relations(
  medicineServicePharmacyMapping,
  ({ one }) => ({
    medicine: one(medicines, {
      fields: [medicineServicePharmacyMapping.medicineId],
      references: [medicines.medicineId]
    }),
    pharmacy: one(pharmacies, {
      fields: [medicineServicePharmacyMapping.pharmacyId],
      references: [pharmacies.pharmacyId]
    }),
    telehealthService: one(telehealthServices, {
      fields: [medicineServicePharmacyMapping.serviceId],
      references: [telehealthServices.id]
    })
  })
);

export const medicinesRelations = relations(medicines, ({ many }) => ({
  medicineServicePharmacyMappings: many(medicineServicePharmacyMapping)
}));

export const chatRoomMembersRelations = relations(
  chatRoomMembers,
  ({ one }) => ({
    user: one(users, {
      fields: [chatRoomMembers.userId],
      references: [users.userId]
    }),
    chatRoom: one(chatRooms, {
      fields: [chatRoomMembers.roomId],
      references: [chatRooms.id]
    })
  })
);

export const chatRoomsRelations = relations(chatRooms, ({ many }) => ({
  chatRoomMembers: many(chatRoomMembers),
  chatMessages: many(chatMessages),
  chatFiles: many(chatFiles)
}));

export const subscriptionPlansRelations = relations(
  subscriptionPlans,
  ({ one, many }) => ({
    telehealthServiceMaster: one(telehealthServiceMaster, {
      fields: [subscriptionPlans.serviceMasterId],
      references: [telehealthServiceMaster.id]
    }),
    user: one(users, {
      fields: [subscriptionPlans.deletedBy],
      references: [users.userId]
    }),
    userSubscriptionBillings: many(userSubscriptionBilling),
    userSubscriptions: many(userSubscription)
  })
);

export const telehealthServiceMasterRelations = relations(
  telehealthServiceMaster,
  ({ one, many }) => ({
    subscriptionPlans: many(subscriptionPlans),
    userSubscriptions: many(userSubscription),
    servicePaymentMappings: many(servicePaymentMapping),
    telehealthServices: many(telehealthServices, {
      relationName:
        'telehealthServices_serviceMasterId_telehealthServiceMaster_id'
    }),
    tennantMaster: one(tennantMaster, {
      fields: [telehealthServiceMaster.tennantId],
      references: [tennantMaster.id]
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceMaster.initialServiceId],
      references: [telehealthServices.id],
      relationName:
        'telehealthServiceMaster_initialServiceId_telehealthServices_id'
    })
  })
);

export const stripeUserPaymentDetailsRelations = relations(
  stripeUserPaymentDetails,
  ({ one }) => ({
    user: one(users, {
      fields: [stripeUserPaymentDetails.userId],
      references: [users.userId]
    })
  })
);

export const telehealthServiceProcedureCodesMappingRelations = relations(
  telehealthServiceProcedureCodesMapping,
  ({ one }) => ({
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceProcedureCodesMapping.serviceId],
      references: [telehealthServices.id]
    }),
    procedureCode: one(procedureCodes, {
      fields: [telehealthServiceProcedureCodesMapping.procedureCodeId],
      references: [procedureCodes.procedureCodeId]
    })
  })
);

export const procedureCodesRelations = relations(
  procedureCodes,
  ({ many }) => ({
    telehealthServiceProcedureCodesMappings: many(
      telehealthServiceProcedureCodesMapping
    )
  })
);

export const telehealthServiceQuestionsRelations = relations(
  telehealthServiceQuestions,
  ({ one, many }) => ({
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceQuestions.serviceId],
      references: [telehealthServices.id]
    }),
    telehealthServiceQuestionAnswers: many(telehealthServiceQuestionAnswer)
  })
);

export const telehealthServiceProviderMappingRelations = relations(
  telehealthServiceProviderMapping,
  ({ one }) => ({
    user: one(users, {
      fields: [telehealthServiceProviderMapping.providerId],
      references: [users.userId]
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceProviderMapping.serviceId],
      references: [telehealthServices.id]
    })
  })
);

export const transactionsRelations = relations(transactions, ({ one }) => ({
  user_payeeUserId: one(users, {
    fields: [transactions.payeeUserId],
    references: [users.userId],
    relationName: 'transactions_payeeUserId_users_userId'
  }),
  user_payerUserId: one(users, {
    fields: [transactions.payerUserId],
    references: [users.userId],
    relationName: 'transactions_payerUserId_users_userId'
  }),
  paymentDetail: one(paymentDetails, {
    fields: [transactions.paymentDetailsId],
    references: [paymentDetails.paymentDetailsId]
  })
}));

export const paymentDetailsRelations = relations(
  paymentDetails,
  ({ many }) => ({
    transactions: many(transactions),
    servicePaymentMappings: many(servicePaymentMapping)
  })
);

export const stripeUserDetailsRelations = relations(
  stripeUserDetails,
  ({ one }) => ({
    user: one(users, {
      fields: [stripeUserDetails.userId],
      references: [users.userId]
    })
  })
);

export const socialHistoryValuesRelations = relations(
  socialHistoryValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [socialHistoryValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);

export const userParticlehealthRelations = relations(
  userParticlehealth,
  ({ one }) => ({
    user_userId: one(users, {
      fields: [userParticlehealth.userId],
      references: [users.userId],
      relationName: 'userParticlehealth_userId_users_userId'
    }),
    user_buserId: one(users, {
      fields: [userParticlehealth.buserId],
      references: [users.userId],
      relationName: 'userParticlehealth_buserId_users_userId'
    })
  })
);

export const userDetailsRelations = relations(userDetails, ({ one }) => ({
  user: one(users, {
    fields: [userDetails.userId],
    references: [users.userId]
  })
}));

export const userEducationalVideosRelations = relations(
  userEducationalVideos,
  ({ one }) => ({
    user_referredBy: one(users, {
      fields: [userEducationalVideos.referredBy],
      references: [users.userId],
      relationName: 'userEducationalVideos_referredBy_users_userId'
    }),
    user_referredFor: one(users, {
      fields: [userEducationalVideos.referredFor],
      references: [users.userId],
      relationName: 'userEducationalVideos_referredFor_users_userId'
    }),
    user_doctorId: one(users, {
      fields: [userEducationalVideos.doctorId],
      references: [users.userId],
      relationName: 'userEducationalVideos_doctorId_users_userId'
    }),
    educationalVideo: one(educationalVideos, {
      fields: [userEducationalVideos.videoId],
      references: [educationalVideos.videoId]
    })
  })
);

export const userIdentitiesRelations = relations(userIdentities, ({ one }) => ({
  user: one(users, {
    fields: [userIdentities.userId],
    references: [users.userId]
  })
}));

export const followUpReminderRelations = relations(
  followUpReminder,
  ({ one }) => ({
    telehealthServiceOrder_orderId: one(telehealthServiceOrder, {
      fields: [followUpReminder.orderId],
      references: [telehealthServiceOrder.id],
      relationName: 'followUpReminder_orderId_telehealthServiceOrder_id'
    }),
    user: one(users, {
      fields: [followUpReminder.userId],
      references: [users.userId]
    }),
    tennantMaster: one(tennantMaster, {
      fields: [followUpReminder.tennantId],
      references: [tennantMaster.id]
    }),
    telehealthServiceOrder_nextOrderId: one(telehealthServiceOrder, {
      fields: [followUpReminder.nextOrderId],
      references: [telehealthServiceOrder.id],
      relationName: 'followUpReminder_nextOrderId_telehealthServiceOrder_id'
    })
  })
);

export const tennantMasterRelations = relations(
  tennantMaster,
  ({ one, many }) => ({
    followUpReminders: many(followUpReminder),
    consultOrderFiles: many(consultOrderFiles),
    prescriptionPreferences: many(prescriptionPreference),
    lifefileConfigurations: many(lifefileConfiguration),
    tennantMaster: one(tennantMaster, {
      fields: [tennantMaster.parentId],
      references: [tennantMaster.id],
      relationName: 'tennantMaster_parentId_tennantMaster_id'
    }),
    tennantMasters: many(tennantMaster, {
      relationName: 'tennantMaster_parentId_tennantMaster_id'
    }),
    user: one(users, {
      fields: [tennantMaster.defaultProviderId],
      references: [users.userId],
      relationName: 'tennantMaster_defaultProviderId_users_userId'
    }),
    users: many(users, {
      relationName: 'users_tennantId_tennantMaster_id'
    }),
    tennantConfigs: many(tennantConfig),
    telehealthServiceMasters: many(telehealthServiceMaster),
    tenantAuthProviders: many(tenantAuthProvider),
    serviceActionPreferences: many(serviceActionPreference),
    userFiles: many(userFiles),
    tenantFiles: many(tenantFiles),
    telehealthServiceCategories: many(telehealthServiceCategory)
  })
);

export const userFileRepoDetailsRelations = relations(
  userFileRepoDetails,
  ({ one }) => ({
    user: one(users, {
      fields: [userFileRepoDetails.userId],
      references: [users.userId]
    })
  })
);

export const userSubscriptionBillingRelations = relations(
  userSubscriptionBilling,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [userSubscriptionBilling.planId],
      references: [subscriptionPlans.planId]
    }),
    user: one(users, {
      fields: [userSubscriptionBilling.userId],
      references: [users.userId]
    })
  })
);

export const drugDaysRelations = relations(drugDays, ({ one }) => ({
  drug: one(drugs, {
    fields: [drugDays.drugId],
    references: [drugs.drugId]
  })
}));

export const userSubscriptionRelations = relations(
  userSubscription,
  ({ one }) => ({
    subscriptionPlan: one(subscriptionPlans, {
      fields: [userSubscription.planId],
      references: [subscriptionPlans.planId]
    }),
    user: one(users, {
      fields: [userSubscription.userId],
      references: [users.userId]
    }),
    telehealthServiceMaster: one(telehealthServiceMaster, {
      fields: [userSubscription.serviceMasterId],
      references: [telehealthServiceMaster.id]
    })
  })
);

export const consultOrderFilesRelations = relations(
  consultOrderFiles,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [consultOrderFiles.orderId],
      references: [telehealthServiceOrder.id]
    }),
    tennantMaster: one(tennantMaster, {
      fields: [consultOrderFiles.tennantId],
      references: [tennantMaster.id]
    })
  })
);

export const smsTemplateRelations = relations(smsTemplate, ({ one }) => ({
  telehealthService: one(telehealthServices, {
    fields: [smsTemplate.serviceId],
    references: [telehealthServices.id]
  })
}));

export const userVitalsDocumentsRelations = relations(
  userVitalsDocuments,
  ({ one }) => ({
    user_doctorId: one(users, {
      fields: [userVitalsDocuments.doctorId],
      references: [users.userId],
      relationName: 'userVitalsDocuments_doctorId_users_userId'
    }),
    user_userId: one(users, {
      fields: [userVitalsDocuments.userId],
      references: [users.userId],
      relationName: 'userVitalsDocuments_userId_users_userId'
    })
  })
);

export const chatMessagesRelations = relations(chatMessages, ({ one }) => ({
  user: one(users, {
    fields: [chatMessages.senderId],
    references: [users.userId]
  }),
  chatRoom: one(chatRooms, {
    fields: [chatMessages.roomId],
    references: [chatRooms.id]
  })
}));

export const jobsRelations = relations(jobs, ({ one }) => ({
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [jobs.orderId],
    references: [telehealthServiceOrder.id]
  }),
  referral: one(referrals, {
    fields: [jobs.referralId],
    references: [referrals.referralId]
  })
}));

export const referralsRelations = relations(referrals, ({ one, many }) => ({
  jobs: many(jobs),
  referralTrackings: many(referralTracking),
  faxes: many(faxes),
  user_doctorId: one(users, {
    fields: [referrals.doctorId],
    references: [users.userId],
    relationName: 'referrals_doctorId_users_userId'
  }),
  order: one(orders, {
    fields: [referrals.orderId],
    references: [orders.id]
  }),
  user_referredBy: one(users, {
    fields: [referrals.referredBy],
    references: [users.userId],
    relationName: 'referrals_referredBy_users_userId'
  }),
  user_referredFor: one(users, {
    fields: [referrals.referredFor],
    references: [users.userId],
    relationName: 'referrals_referredFor_users_userId'
  }),
  product: one(products, {
    fields: [referrals.productId],
    references: [products.productId]
  })
}));

export const userSchedulesRelations = relations(userSchedules, ({ one }) => ({
  user_userId: one(users, {
    fields: [userSchedules.userId],
    references: [users.userId],
    relationName: 'userSchedules_userId_users_userId'
  }),
  user_createdBy: one(users, {
    fields: [userSchedules.createdBy],
    references: [users.userId],
    relationName: 'userSchedules_createdBy_users_userId'
  }),
  user_updatedBy: one(users, {
    fields: [userSchedules.updatedBy],
    references: [users.userId],
    relationName: 'userSchedules_updatedBy_users_userId'
  }),
  user_deletedBy: one(users, {
    fields: [userSchedules.deletedBy],
    references: [users.userId],
    relationName: 'userSchedules_deletedBy_users_userId'
  })
}));

export const chatFilesRelations = relations(chatFiles, ({ one }) => ({
  user: one(users, {
    fields: [chatFiles.userId],
    references: [users.userId]
  }),
  chatRoom: one(chatRooms, {
    fields: [chatFiles.roomId],
    references: [chatRooms.id]
  })
}));

export const emailTemplateRelations = relations(emailTemplate, ({ one }) => ({
  telehealthService: one(telehealthServices, {
    fields: [emailTemplate.serviceId],
    references: [telehealthServices.id]
  })
}));

export const prescriptionPreferenceRelations = relations(
  prescriptionPreference,
  ({ one }) => ({
    pharmacy: one(pharmacies, {
      fields: [prescriptionPreference.pharmacyId],
      references: [pharmacies.pharmacyId]
    }),
    tennantMaster: one(tennantMaster, {
      fields: [prescriptionPreference.tennantId],
      references: [tennantMaster.id]
    })
  })
);

export const lifefileConfigurationRelations = relations(
  lifefileConfiguration,
  ({ one }) => ({
    tennantMaster: one(tennantMaster, {
      fields: [lifefileConfiguration.tennantId],
      references: [tennantMaster.id]
    }),
    pharmacy: one(pharmacies, {
      fields: [lifefileConfiguration.pharmacyId],
      references: [pharmacies.pharmacyId]
    })
  })
);

export const telehealthServiceQuestionAnswerDumpRelations = relations(
  telehealthServiceQuestionAnswerDump,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [telehealthServiceQuestionAnswerDump.serviceOrderId],
      references: [telehealthServiceOrder.id]
    }),
    user: one(users, {
      fields: [telehealthServiceQuestionAnswerDump.answeredBy],
      references: [users.userId]
    })
  })
);

export const consultReassignHistoryRelations = relations(
  consultReassignHistory,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [consultReassignHistory.serviceOrderId],
      references: [telehealthServiceOrder.id]
    }),
    user_previousProvider: one(users, {
      fields: [consultReassignHistory.previousProvider],
      references: [users.userId],
      relationName: 'consultReassignHistory_previousProvider_users_userId'
    }),
    user_updatedProvider: one(users, {
      fields: [consultReassignHistory.updatedProvider],
      references: [users.userId],
      relationName: 'consultReassignHistory_updatedProvider_users_userId'
    }),
    user_reassignedBy: one(users, {
      fields: [consultReassignHistory.reassignedBy],
      references: [users.userId],
      relationName: 'consultReassignHistory_reassignedBy_users_userId'
    })
  })
);

export const externalRequestsLogRelations = relations(
  externalRequestsLog,
  ({ one }) => ({
    user: one(users, {
      fields: [externalRequestsLog.userId],
      references: [users.userId]
    })
  })
);

export const mmsPatientInvitationsRelations = relations(
  mmsPatientInvitations,
  ({ one }) => ({
    mmsPatient: one(mmsPatients, {
      fields: [mmsPatientInvitations.mmsPatientId],
      references: [mmsPatients.patientId]
    })
  })
);

export const servicePaymentMappingRelations = relations(
  servicePaymentMapping,
  ({ one }) => ({
    telehealthServiceMaster: one(telehealthServiceMaster, {
      fields: [servicePaymentMapping.serviceMasterId],
      references: [telehealthServiceMaster.id]
    }),
    paymentDetail: one(paymentDetails, {
      fields: [servicePaymentMapping.paymentDetailsId],
      references: [paymentDetails.paymentDetailsId]
    })
  })
);

export const referralTrackingRelations = relations(
  referralTracking,
  ({ one }) => ({
    referral: one(referrals, {
      fields: [referralTracking.referralId],
      references: [referrals.referralId]
    }),
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [referralTracking.orderGuid],
      references: [telehealthServiceOrder.orderGuid]
    })
  })
);

export const supportNotesRelations = relations(supportNotes, ({ one }) => ({
  user_userId: one(users, {
    fields: [supportNotes.userId],
    references: [users.userId],
    relationName: 'supportNotes_userId_users_userId'
  }),
  user_supportUserId: one(users, {
    fields: [supportNotes.supportUserId],
    references: [users.userId],
    relationName: 'supportNotes_supportUserId_users_userId'
  }),
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [supportNotes.orderGuid],
    references: [telehealthServiceOrder.orderGuid]
  })
}));

export const tennantConfigRelations = relations(tennantConfig, ({ one }) => ({
  tennantMaster: one(tennantMaster, {
    fields: [tennantConfig.tennantId],
    references: [tennantMaster.id]
  })
}));

export const tenantAuthProviderRelations = relations(
  tenantAuthProvider,
  ({ one }) => ({
    tennantMaster: one(tennantMaster, {
      fields: [tenantAuthProvider.tenantId],
      references: [tennantMaster.id]
    })
  })
);

export const serviceActionPreferenceRelations = relations(
  serviceActionPreference,
  ({ one }) => ({
    telehealthService: one(telehealthServices, {
      fields: [serviceActionPreference.serviceId],
      references: [telehealthServices.id]
    }),
    tennantMaster: one(tennantMaster, {
      fields: [serviceActionPreference.tenantId],
      references: [tennantMaster.id]
    })
  })
);

export const diagnosesValuesRelations = relations(
  diagnosesValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [diagnosesValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);

export const familyHistoryValuesRelations = relations(
  familyHistoryValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [familyHistoryValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);

export const proceduresValuesRelations = relations(
  proceduresValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [proceduresValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);

export const carePlanValuesRelations = relations(carePlanValues, ({ one }) => ({
  userHealthSummary: one(userHealthSummary, {
    fields: [carePlanValues.summaryId],
    references: [userHealthSummary.summaryId]
  })
}));

export const resultsValuesRelations = relations(resultsValues, ({ one }) => ({
  userHealthSummary: one(userHealthSummary, {
    fields: [resultsValues.summaryId],
    references: [userHealthSummary.summaryId]
  })
}));

export const allergiesValuesRelations = relations(
  allergiesValues,
  ({ one }) => ({
    userHealthSummary: one(userHealthSummary, {
      fields: [allergiesValues.summaryId],
      references: [userHealthSummary.summaryId]
    })
  })
);

export const documentValuesRelations = relations(documentValues, ({ one }) => ({
  userHealthSummary: one(userHealthSummary, {
    fields: [documentValues.summaryId],
    references: [userHealthSummary.summaryId]
  })
}));

export const mmsPrescriptionRefillsRelations = relations(
  mmsPrescriptionRefills,
  ({ one }) => ({
    mmsPrescription: one(mmsPrescriptions, {
      fields: [mmsPrescriptionRefills.prescriptionId],
      references: [mmsPrescriptions.prescriptionId]
    })
  })
);

export const vitalsSummaryUploadStatusRelations = relations(
  vitalsSummaryUploadStatus,
  ({ one }) => ({
    user_doctorId: one(users, {
      fields: [vitalsSummaryUploadStatus.doctorId],
      references: [users.userId],
      relationName: 'vitalsSummaryUploadStatus_doctorId_users_userId'
    }),
    user_patientId: one(users, {
      fields: [vitalsSummaryUploadStatus.patientId],
      references: [users.userId],
      relationName: 'vitalsSummaryUploadStatus_patientId_users_userId'
    })
  })
);

export const vitalsNotificationCycleRelations = relations(
  vitalsNotificationCycle,
  ({ one }) => ({
    user: one(users, {
      fields: [vitalsNotificationCycle.userId],
      references: [users.userId]
    })
  })
);

export const visitSummaryUploadStatusRelations = relations(
  visitSummaryUploadStatus,
  ({ one }) => ({
    order: one(orders, {
      fields: [visitSummaryUploadStatus.orderId],
      references: [orders.orderId]
    }),
    user: one(users, {
      fields: [visitSummaryUploadStatus.userId],
      references: [users.userId]
    })
  })
);

export const vitalsPatientMonitoringRelations = relations(
  vitalsPatientMonitoring,
  ({ one }) => ({
    user_buserId: one(users, {
      fields: [vitalsPatientMonitoring.buserId],
      references: [users.userId],
      relationName: 'vitalsPatientMonitoring_buserId_users_userId'
    }),
    user_patientId: one(users, {
      fields: [vitalsPatientMonitoring.patientId],
      references: [users.userId],
      relationName: 'vitalsPatientMonitoring_patientId_users_userId'
    })
  })
);

export const userFormsRelations = relations(userForms, ({ one }) => ({
  user_assignedBy: one(users, {
    fields: [userForms.assignedBy],
    references: [users.userId],
    relationName: 'userForms_assignedBy_users_userId'
  }),
  user_assignedTo: one(users, {
    fields: [userForms.assignedTo],
    references: [users.userId],
    relationName: 'userForms_assignedTo_users_userId'
  }),
  user_doctorId: one(users, {
    fields: [userForms.doctorId],
    references: [users.userId],
    relationName: 'userForms_doctorId_users_userId'
  }),
  form: one(forms, {
    fields: [userForms.formId],
    references: [forms.formId]
  }),
  order: one(orders, {
    fields: [userForms.orderId],
    references: [orders.id]
  })
}));

export const formsRelations = relations(forms, ({ many }) => ({
  userForms: many(userForms)
}));

export const userDietRelations = relations(userDiet, ({ one }) => ({
  order: one(orders, {
    fields: [userDiet.orderId],
    references: [orders.id]
  }),
  user: one(users, {
    fields: [userDiet.userId],
    references: [users.userId]
  })
}));

export const telehealthServiceQuestionAnswerRelations = relations(
  telehealthServiceQuestionAnswer,
  ({ one }) => ({
    telehealthServiceQuestion: one(telehealthServiceQuestions, {
      fields: [telehealthServiceQuestionAnswer.questionId],
      references: [telehealthServiceQuestions.id]
    }),
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [telehealthServiceQuestionAnswer.serviceOrderId],
      references: [telehealthServiceOrder.id]
    })
  })
);

export const faxesRelations = relations(faxes, ({ one }) => ({
  referral: one(referrals, {
    fields: [faxes.referralId],
    references: [referrals.referralId]
  }),
  user_sentBy: one(users, {
    fields: [faxes.sentBy],
    references: [users.userId],
    relationName: 'faxes_sentBy_users_userId'
  }),
  user_sentFor: one(users, {
    fields: [faxes.sentFor],
    references: [users.userId],
    relationName: 'faxes_sentFor_users_userId'
  })
}));

export const reviewsRelations = relations(reviews, ({ one }) => ({
  user_givenBy: one(users, {
    fields: [reviews.givenBy],
    references: [users.userId],
    relationName: 'reviews_givenBy_users_userId'
  }),
  user_givenTo: one(users, {
    fields: [reviews.givenTo],
    references: [users.userId],
    relationName: 'reviews_givenTo_users_userId'
  }),
  order: one(orders, {
    fields: [reviews.orderId],
    references: [orders.orderId]
  })
}));

export const productsRelations = relations(products, ({ many }) => ({
  referrals: many(referrals)
}));

export const transcriptionsRelations = relations(transcriptions, ({ one }) => ({
  order: one(orders, {
    fields: [transcriptions.orderId],
    references: [orders.orderId]
  }),
  user: one(users, {
    fields: [transcriptions.tuserId],
    references: [users.userId]
  })
}));

export const userVitalsRelations = relations(userVitals, ({ one }) => ({
  order: one(orders, {
    fields: [userVitals.orderId],
    references: [orders.id]
  }),
  user: one(users, {
    fields: [userVitals.userId],
    references: [users.userId]
  })
}));

export const providerRatingsRelations = relations(
  providerRatings,
  ({ one }) => ({
    order: one(orders, {
      fields: [providerRatings.orderId],
      references: [orders.id]
    }),
    user_providerId: one(users, {
      fields: [providerRatings.providerId],
      references: [users.userId],
      relationName: 'providerRatings_providerId_users_userId'
    }),
    user_userId: one(users, {
      fields: [providerRatings.userId],
      references: [users.userId],
      relationName: 'providerRatings_userId_users_userId'
    })
  })
);

export const healthSummariesLogRelations = relations(
  healthSummariesLog,
  ({ one }) => ({
    user: one(users, {
      fields: [healthSummariesLog.userId],
      references: [users.userId]
    })
  })
);

export const conversationMessagesRelations = relations(
  conversationMessages,
  ({ one }) => ({
    conversation: one(conversations, {
      fields: [conversationMessages.cId],
      references: [conversations.cId]
    }),
    user: one(users, {
      fields: [conversationMessages.userId],
      references: [users.userId]
    })
  })
);

export const conversationsRelations = relations(
  conversations,
  ({ one, many }) => ({
    conversationMessages: many(conversationMessages),
    user_userOne: one(users, {
      fields: [conversations.userOne],
      references: [users.userId],
      relationName: 'conversations_userOne_users_userId'
    }),
    user_userTwo: one(users, {
      fields: [conversations.userTwo],
      references: [users.userId],
      relationName: 'conversations_userTwo_users_userId'
    })
  })
);

export const prescriptionTransferMedicationsRelations = relations(
  prescriptionTransferMedications,
  ({ one }) => ({
    prescriptionTransferRequest: one(prescriptionTransferRequest, {
      fields: [prescriptionTransferMedications.requestId],
      references: [prescriptionTransferRequest.requestId]
    })
  })
);

export const prescriptionTransferRequestRelations = relations(
  prescriptionTransferRequest,
  ({ one, many }) => ({
    prescriptionTransferMedications: many(prescriptionTransferMedications),
    user: one(users, {
      fields: [prescriptionTransferRequest.userId],
      references: [users.userId]
    })
  })
);

export const refillRequestRelations = relations(refillRequest, ({ one }) => ({
  telehealthServiceOrder: one(telehealthServiceOrder, {
    fields: [refillRequest.serviceOrderId],
    references: [telehealthServiceOrder.id]
  })
}));

export const telehealthServiceStateMappingRelations = relations(
  telehealthServiceStateMapping,
  ({ one }) => ({
    state: one(states, {
      fields: [telehealthServiceStateMapping.stateId],
      references: [states.stateId]
    }),
    telehealthService: one(telehealthServices, {
      fields: [telehealthServiceStateMapping.serviceId],
      references: [telehealthServices.id]
    })
  })
);

export const pharmacyStateServiceMappingRelations = relations(
  pharmacyStateServiceMapping,
  ({ one }) => ({
    telehealthService: one(telehealthServices, {
      fields: [pharmacyStateServiceMapping.serviceId],
      references: [telehealthServices.id]
    }),
    state: one(states, {
      fields: [pharmacyStateServiceMapping.stateId],
      references: [states.stateId]
    }),
    pharmacy: one(pharmacies, {
      fields: [pharmacyStateServiceMapping.pharmacyId],
      references: [pharmacies.pharmacyId]
    })
  })
);

export const userInsuranceRelations = relations(userInsurance, ({ one }) => ({
  user: one(users, {
    fields: [userInsurance.userId],
    references: [users.userId]
  })
}));

export const insuranceEligibilityLogsRelations = relations(
  insuranceEligibilityLogs,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [insuranceEligibilityLogs.orderId],
      references: [telehealthServiceOrder.id]
    })
  })
);

export const userExternalIdMappingRelations = relations(
  userExternalIdMapping,
  ({ one }) => ({
    user: one(users, {
      fields: [userExternalIdMapping.userId],
      references: [users.userId]
    })
  })
);

export const userFilesRelations = relations(userFiles, ({ one }) => ({
  user_createdBy: one(users, {
    fields: [userFiles.createdBy],
    references: [users.userId],
    relationName: 'userFiles_createdBy_users_userId'
  }),
  user_doctorId: one(users, {
    fields: [userFiles.doctorId],
    references: [users.userId],
    relationName: 'userFiles_doctorId_users_userId'
  }),
  order: one(orders, {
    fields: [userFiles.orderId],
    references: [orders.id]
  }),
  user_userId: one(users, {
    fields: [userFiles.userId],
    references: [users.userId],
    relationName: 'userFiles_userId_users_userId'
  }),
  tennantMaster: one(tennantMaster, {
    fields: [userFiles.tenantId],
    references: [tennantMaster.id]
  })
}));

export const consultNoteTemplatesRelations = relations(
  consultNoteTemplates,
  ({ one }) => ({
    user: one(users, {
      fields: [consultNoteTemplates.userId],
      references: [users.userId]
    })
  })
);

export const tenantFilesRelations = relations(tenantFiles, ({ one }) => ({
  tennantMaster: one(tennantMaster, {
    fields: [tenantFiles.tenantId],
    references: [tennantMaster.id]
  }),
  telehealthService: one(telehealthServices, {
    fields: [tenantFiles.serviceId],
    references: [telehealthServices.id]
  })
}));

export const telehealthServiceCategoryRelations = relations(
  telehealthServiceCategory,
  ({ one }) => ({
    tennantMaster: one(tennantMaster, {
      fields: [telehealthServiceCategory.tennantId],
      references: [tennantMaster.id]
    })
  })
);

export const userLicensesRelations = relations(userLicenses, ({ one }) => ({
  user: one(users, {
    fields: [userLicenses.userId],
    references: [users.userId]
  }),
  state: one(states, {
    fields: [userLicenses.stateId],
    references: [states.stateId]
  })
}));

export const notificationReminderRelations = relations(
  notificationReminder,
  ({ one }) => ({
    telehealthServiceOrder: one(telehealthServiceOrder, {
      fields: [notificationReminder.orderId],
      references: [telehealthServiceOrder.id]
    }),
    user: one(users, {
      fields: [notificationReminder.userId],
      references: [users.userId]
    })
  })
);

export const userPracticeGroupsRelations = relations(
  userPracticeGroups,
  ({ one }) => ({
    practiceGroup: one(practiceGroups, {
      fields: [userPracticeGroups.practiceGroupId],
      references: [practiceGroups.practiceGroupId]
    }),
    user: one(users, {
      fields: [userPracticeGroups.userId],
      references: [users.userId]
    })
  })
);

export const practiceGroupsRelations = relations(
  practiceGroups,
  ({ many }) => ({
    userPracticeGroups: many(userPracticeGroups)
  })
);

export const scheduleTranslationRelations = relations(
  scheduleTranslation,
  ({ one }) => ({
    schedule: one(schedules, {
      fields: [scheduleTranslation.scheduleId],
      references: [schedules.scheduleId]
    })
  })
);

export const requestTranslationRelations = relations(
  requestTranslation,
  ({ one }) => ({
    request: one(requests, {
      fields: [requestTranslation.requestId],
      references: [requests.requestId]
    })
  })
);

export const usersTranslationRelations = relations(
  usersTranslation,
  ({ one }) => ({
    user: one(users, {
      fields: [usersTranslation.userId],
      references: [users.userId]
    })
  })
);

export const userViewersRelations = relations(userViewers, ({ one }) => ({
  user_userId: one(users, {
    fields: [userViewers.userId],
    references: [users.userId],
    relationName: 'userViewers_userId_users_userId'
  }),
  user_viewerId: one(users, {
    fields: [userViewers.viewerId],
    references: [users.userId],
    relationName: 'userViewers_viewerId_users_userId'
  })
}));

export const userAssociationsRelations = relations(
  userAssociations,
  ({ one }) => ({
    user_buserId: one(users, {
      fields: [userAssociations.buserId],
      references: [users.userId],
      relationName: 'userAssociations_buserId_users_userId'
    }),
    user_userId: one(users, {
      fields: [userAssociations.userId],
      references: [users.userId],
      relationName: 'userAssociations_userId_users_userId'
    })
  })
);
