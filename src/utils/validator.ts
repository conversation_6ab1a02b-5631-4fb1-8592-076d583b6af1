/**
 * @fileoverview Request validation middleware using Zod
 * Provides type-safe validation for request params, query, body and response
 */

import type { NextFunction, Request, Response } from 'express';
import { z, type AnyZodObject } from 'zod';
import { BackendError } from './errors';
import type { ValidationErrorDetail } from './errors';

/**
 * Schema for validating different parts of an HTTP request
 */
export type ValidationSchema = {
  params?: AnyZodObject;
  query?: AnyZodObject;
  body?: AnyZodObject;
};

/**
 * Schema for validating HTTP response
 */
export type ResponseSchema = {
  body?: AnyZodObject;
};

/**
 * Complete schema for validating both request and response
 */
export type RequestValidationSchema = {
  request?: ValidationSchema;
  response?: ResponseSchema;
};

/**
 * Type guard to check if an error is a Zod error
 */
const isZodError = (error: unknown): error is z.ZodError => {
  return error instanceof z.ZodError;
};

/**
 * Formats Zod validation errors into ValidationErrorDetail array
 */
const formatValidationError = (
  error: z.Zod<PERSON>rror,
  context: 'params' | 'query' | 'body' | 'response'
): ValidationErrorDetail[] => {
  return error.errors.map((err) => ({
    path: err.path.join('.'),
    message: err.message,
    code: err.code
  }));
};

/**
 * Creates a middleware function that validates requests against the provided schema
 * @param schema - Validation schema for request and response
 * @returns Express middleware function
 */
export const validate = (schema: RequestValidationSchema) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validate URL parameters
      if (schema.request?.params) {
        try {
          req.params = await schema.request.params.parseAsync(req.params);
        } catch (error) {
          if (isZodError(error)) {
            throw new BackendError('VALIDATION_ERROR', {
              message: 'Invalid URL parameters',
              context: 'params',
              details: formatValidationError(error, 'params'),
              statusCode: 400
            });
          }
          throw error;
        }
      }

      // Validate query parameters
      if (schema.request?.query) {
        try {
          if (Object.keys(req.query).length === 0) {
            throw new BackendError('VALIDATION_ERROR', {
              message: 'Query parameters are required but none were provided',
              context: 'query',
              statusCode: 400
            });
          }
          req.query = await schema.request.query.parseAsync(req.query);
        } catch (error) {
          if (isZodError(error)) {
            throw new BackendError('VALIDATION_ERROR', {
              message: 'Invalid or missing query parameters',
              context: 'query',
              details: formatValidationError(error, 'query'),
              statusCode: 400
            });
          }
          throw error;
        }
      }

      // Validate request body
      if (schema.request?.body) {
        try {
          if (!req.body || Object.keys(req.body).length === 0) {
            throw new BackendError('VALIDATION_ERROR', {
              message: 'Request body is required but was empty',
              context: 'body',
              statusCode: 400
            });
          }
          req.body = await schema.request.body.parseAsync(req.body);
        } catch (error) {
          if (isZodError(error)) {
            throw new BackendError('VALIDATION_ERROR', {
              message: 'Invalid request body',
              context: 'body',
              details: formatValidationError(error, 'body'),
              statusCode: 400
            });
          }
          throw error;
        }
      }

      // Override res.json to validate response body
      const originalJson = res.json;
      res.json = function (data: unknown) {
        if (schema.response?.body) {
          try {
            const validatedData = schema.response.body.parse(data);
            return originalJson.call(this, validatedData);
          } catch (error) {
            if (isZodError(error)) {
              throw new BackendError('VALIDATION_ERROR', {
                message: 'Invalid response format',
                context: 'response',
                details: formatValidationError(error, 'response'),
                statusCode: 500
              });
            }
            throw error;
          }
        }
        return originalJson.call(this, data);
      };

      return next();
    } catch (error) {
      if (error instanceof BackendError) {
        next(error);
        return;
      }
      next(
        new BackendError('INTERNAL_ERROR', {
          message: 'An unexpected error occurred during validation',
          details: {
            error: error instanceof Error ? error.message : String(error)
          }
        })
      );
    }
  };
};
